<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>編輯願景板 - 感恩願景App</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            background: #F8F9FA;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            position: relative;
        }

        .header {
            background: linear-gradient(135deg, #45B7D1, #96CEB4);
            padding: 50px 20px 20px;
            position: relative;
        }

        .header-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .back-button {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 20px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: rgba(255,255,255,0.3);
        }

        .save-button {
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 20px;
            color: white;
            cursor: pointer;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .save-button:hover {
            background: rgba(255,255,255,0.3);
        }

        .vision-title {
            color: white;
            font-size: 24px;
            font-weight: bold;
            text-align: center;
        }

        .content {
            padding: 20px;
            padding-bottom: 100px;
        }

        .canvas-container {
            background: white;
            border-radius: 16px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            position: relative;
            height: 400px;
        }

        .canvas-background {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
            position: relative;
            overflow: hidden;
        }

        .vision-element {
            position: absolute;
            cursor: move;
            user-select: none;
            transition: transform 0.2s ease;
        }

        .vision-element.selected {
            transform: scale(1.1);
            z-index: 10;
        }

        .vision-element.dragging {
            z-index: 20;
            transform: scale(1.1) rotate(5deg);
        }

        .text-element {
            background: rgba(255,255,255,0.9);
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            min-width: 60px;
            text-align: center;
        }

        .emoji-element {
            font-size: 32px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .image-element {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            background: rgba(255,255,255,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .element-controls {
            position: absolute;
            top: -30px;
            right: -10px;
            display: none;
            gap: 4px;
        }

        .vision-element.selected .element-controls {
            display: flex;
        }

        .control-button {
            width: 24px;
            height: 24px;
            border-radius: 12px;
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .toolbar {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .toolbar-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .toolbar-title .material-icons {
            color: #45B7D1;
        }

        .tool-section {
            margin-bottom: 20px;
        }

        .tool-section:last-child {
            margin-bottom: 0;
        }

        .section-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }

        .tool-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
        }

        .tool-item {
            aspect-ratio: 1;
            border-radius: 12px;
            background: #F8F9FA;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            border: 2px solid transparent;
        }

        .tool-item:hover {
            background: #E5E5EA;
            transform: scale(1.05);
        }

        .tool-item.active {
            border-color: #45B7D1;
            background: #45B7D120;
        }

        .color-palette {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 8px;
        }

        .color-item {
            aspect-ratio: 1;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .color-item:hover {
            transform: scale(1.1);
        }

        .color-item.active {
            border-color: #333;
            transform: scale(1.1);
        }

        .text-input-section {
            margin-bottom: 16px;
        }

        .text-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #E5E5EA;
            border-radius: 12px;
            font-size: 16px;
            font-family: inherit;
            margin-bottom: 8px;
        }

        .add-text-button {
            width: 100%;
            background: #45B7D1;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .add-text-button:hover {
            background: #3A9BC1;
        }

        .settings-section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .setting-item:last-child {
            margin-bottom: 0;
        }

        .setting-label {
            font-size: 14px;
            color: #333;
        }

        .setting-input {
            padding: 8px 12px;
            border: 1px solid #E5E5EA;
            border-radius: 8px;
            font-size: 14px;
            width: 120px;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #E5E5EA;
            padding: 8px 0;
            display: flex;
            justify-content: space-around;
            z-index: 100;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #8E8E93;
        }

        .nav-item.active {
            color: #FF6B6B;
        }

        .nav-item .material-icons {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .nav-text {
            font-size: 12px;
            font-weight: 500;
        }

        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 頂部導航 -->
        <div class="header">
            <div class="header-nav">
                <button class="back-button" onclick="goBack()">
                    <span class="material-icons">arrow_back</span>
                </button>
                <button class="save-button" onclick="saveVision()">保存</button>
            </div>
            <div class="vision-title">健康生活</div>
        </div>

        <!-- 主要內容 -->
        <div class="content">
            <!-- 畫布區域 -->
            <div class="canvas-container fade-in">
                <div class="canvas-background" id="canvas">
                    <!-- 預設元素 -->
                    <div class="vision-element text-element" style="top: 50px; left: 50px;" onclick="selectElement(this)">
                        健康生活
                        <div class="element-controls">
                            <button class="control-button" onclick="editElement(this)">
                                <span class="material-icons" style="font-size: 12px;">edit</span>
                            </button>
                            <button class="control-button" onclick="deleteElement(this)">
                                <span class="material-icons" style="font-size: 12px;">delete</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="vision-element emoji-element" style="top: 120px; left: 80px;" onclick="selectElement(this)">
                        🏃
                        <div class="element-controls">
                            <button class="control-button" onclick="editElement(this)">
                                <span class="material-icons" style="font-size: 12px;">edit</span>
                            </button>
                            <button class="control-button" onclick="deleteElement(this)">
                                <span class="material-icons" style="font-size: 12px;">delete</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="vision-element emoji-element" style="top: 200px; left: 150px;" onclick="selectElement(this)">
                        🥗
                        <div class="element-controls">
                            <button class="control-button" onclick="editElement(this)">
                                <span class="material-icons" style="font-size: 12px;">edit</span>
                            </button>
                            <button class="control-button" onclick="deleteElement(this)">
                                <span class="material-icons" style="font-size: 12px;">delete</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="vision-element emoji-element" style="top: 280px; left: 200px;" onclick="selectElement(this)">
                        💪
                        <div class="element-controls">
                            <button class="control-button" onclick="editElement(this)">
                                <span class="material-icons" style="font-size: 12px;">edit</span>
                            </button>
                            <button class="control-button" onclick="deleteElement(this)">
                                <span class="material-icons" style="font-size: 12px;">delete</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 工具欄 -->
            <div class="toolbar fade-in">
                <div class="toolbar-title">
                    <span class="material-icons">build</span>
                    編輯工具
                </div>

                <!-- 添加文字 -->
                <div class="tool-section">
                    <div class="section-title">添加文字</div>
                    <div class="text-input-section">
                        <input type="text" class="text-input" id="textInput" placeholder="輸入文字內容...">
                        <button class="add-text-button" onclick="addText()">添加文字</button>
                    </div>
                </div>

                <!-- Emoji選擇 -->
                <div class="tool-section">
                    <div class="section-title">添加Emoji</div>
                    <div class="tool-grid">
                        <div class="tool-item" onclick="addEmoji('🏃')">🏃</div>
                        <div class="tool-item" onclick="addEmoji('🥗')">🥗</div>
                        <div class="tool-item" onclick="addEmoji('💪')">💪</div>
                        <div class="tool-item" onclick="addEmoji('🧘')">🧘</div>
                        <div class="tool-item" onclick="addEmoji('🏋️')">🏋️</div>
                        <div class="tool-item" onclick="addEmoji('🚴')">🚴</div>
                        <div class="tool-item" onclick="addEmoji('🏊')">🏊</div>
                        <div class="tool-item" onclick="addEmoji('⚽')">⚽</div>
                    </div>
                </div>

                <!-- 背景顏色 -->
                <div class="tool-section">
                    <div class="section-title">背景顏色</div>
                    <div class="color-palette">
                        <div class="color-item active" style="background: linear-gradient(135deg, #FF6B6B, #FF8E8E);" onclick="changeBackground(this, 'linear-gradient(135deg, #FF6B6B, #FF8E8E)')"></div>
                        <div class="color-item" style="background: linear-gradient(135deg, #4ECDC4, #6DD4CB);" onclick="changeBackground(this, 'linear-gradient(135deg, #4ECDC4, #6DD4CB)')"></div>
                        <div class="color-item" style="background: linear-gradient(135deg, #45B7D1, #6BC5D8);" onclick="changeBackground(this, 'linear-gradient(135deg, #45B7D1, #6BC5D8)')"></div>
                        <div class="color-item" style="background: linear-gradient(135deg, #96CEB4, #A8D5C1);" onclick="changeBackground(this, 'linear-gradient(135deg, #96CEB4, #A8D5C1)')"></div>
                        <div class="color-item" style="background: linear-gradient(135deg, #FFEAA7, #FFEFB8);" onclick="changeBackground(this, 'linear-gradient(135deg, #FFEAA7, #FFEFB8)')"></div>
                        <div class="color-item" style="background: linear-gradient(135deg, #DDA0DD, #E6B3E6);" onclick="changeBackground(this, 'linear-gradient(135deg, #DDA0DD, #E6B3E6)')"></div>
                    </div>
                </div>
            </div>

            <!-- 設置區域 -->
            <div class="settings-section fade-in">
                <div class="toolbar-title">
                    <span class="material-icons">settings</span>
                    願景設置
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">願景標題</div>
                    <input type="text" class="setting-input" value="健康生活" onchange="updateTitle(this.value)">
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">目標日期</div>
                    <input type="date" class="setting-input" value="2024-12-31">
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">分類標籤</div>
                    <select class="setting-input">
                        <option value="health">健康</option>
                        <option value="career">事業</option>
                        <option value="relationship">關係</option>
                        <option value="learning">學習</option>
                        <option value="finance">財務</option>
                        <option value="life">生活</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 底部導航 -->
        <div class="bottom-nav">
            <a href="main-app.html" class="nav-item">
                <span class="material-icons">home</span>
                <span class="nav-text">首頁</span>
            </a>
            <a href="diary.html" class="nav-item">
                <span class="material-icons">book</span>
                <span class="nav-text">日記</span>
            </a>
            <a href="vision-board.html" class="nav-item active">
                <span class="material-icons">dashboard</span>
                <span class="nav-text">願景板</span>
            </a>
            <a href="statistics.html" class="nav-item">
                <span class="material-icons">analytics</span>
                <span class="nav-text">統計</span>
            </a>
            <a href="profile.html" class="nav-item">
                <span class="material-icons">person</span>
                <span class="nav-text">個人</span>
            </a>
        </div>
    </div>

    <script>
        let selectedElement = null;
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };

        // 返回上一頁
        function goBack() {
            window.location.href = 'vision-board.html';
        }

        // 保存願景板
        function saveVision() {
            alert('願景板已保存！');
            goBack();
        }

        // 選擇元素
        function selectElement(element) {
            // 移除其他元素的選中狀態
            document.querySelectorAll('.vision-element').forEach(el => {
                el.classList.remove('selected');
            });
            
            // 選中當前元素
            element.classList.add('selected');
            selectedElement = element;
        }

        // 添加文字
        function addText() {
            const textInput = document.getElementById('textInput');
            const text = textInput.value.trim();
            
            if (!text) {
                alert('請輸入文字內容');
                return;
            }
            
            const canvas = document.getElementById('canvas');
            const textElement = document.createElement('div');
            textElement.className = 'vision-element text-element';
            textElement.style.top = '100px';
            textElement.style.left = '100px';
            textElement.onclick = function() { selectElement(this); };
            textElement.innerHTML = `
                ${text}
                <div class="element-controls">
                    <button class="control-button" onclick="editElement(this)">
                        <span class="material-icons" style="font-size: 12px;">edit</span>
                    </button>
                    <button class="control-button" onclick="deleteElement(this)">
                        <span class="material-icons" style="font-size: 12px;">delete</span>
                    </button>
                </div>
            `;
            
            canvas.appendChild(textElement);
            textInput.value = '';
            selectElement(textElement);
            makeDraggable(textElement);
        }

        // 添加Emoji
        function addEmoji(emoji) {
            const canvas = document.getElementById('canvas');
            const emojiElement = document.createElement('div');
            emojiElement.className = 'vision-element emoji-element';
            emojiElement.style.top = '150px';
            emojiElement.style.left = '150px';
            emojiElement.onclick = function() { selectElement(this); };
            emojiElement.innerHTML = `
                ${emoji}
                <div class="element-controls">
                    <button class="control-button" onclick="editElement(this)">
                        <span class="material-icons" style="font-size: 12px;">edit</span>
                    </button>
                    <button class="control-button" onclick="deleteElement(this)">
                        <span class="material-icons" style="font-size: 12px;">delete</span>
                    </button>
                </div>
            `;
            
            canvas.appendChild(emojiElement);
            selectElement(emojiElement);
            makeDraggable(emojiElement);
        }

        // 更改背景
        function changeBackground(colorElement, gradient) {
            document.querySelectorAll('.color-item').forEach(item => {
                item.classList.remove('active');
            });
            colorElement.classList.add('active');
            
            document.getElementById('canvas').style.background = gradient;
        }

        // 編輯元素
        function editElement(button) {
            const element = button.closest('.vision-element');
            const currentText = element.textContent.trim();
            const newText = prompt('編輯內容：', currentText);
            
            if (newText && newText !== currentText) {
                if (element.classList.contains('text-element')) {
                    element.innerHTML = `
                        ${newText}
                        <div class="element-controls">
                            <button class="control-button" onclick="editElement(this)">
                                <span class="material-icons" style="font-size: 12px;">edit</span>
                            </button>
                            <button class="control-button" onclick="deleteElement(this)">
                                <span class="material-icons" style="font-size: 12px;">delete</span>
                            </button>
                        </div>
                    `;
                }
            }
        }

        // 刪除元素
        function deleteElement(button) {
            const element = button.closest('.vision-element');
            if (confirm('確定要刪除這個元素嗎？')) {
                element.remove();
                selectedElement = null;
            }
        }

        // 更新標題
        function updateTitle(newTitle) {
            document.querySelector('.vision-title').textContent = newTitle;
        }

        // 使元素可拖拽
        function makeDraggable(element) {
            element.addEventListener('mousedown', startDrag);
            element.addEventListener('touchstart', startDrag);
        }

        function startDrag(e) {
            e.preventDefault();
            isDragging = true;
            selectedElement = e.target.closest('.vision-element');
            selectedElement.classList.add('dragging');
            
            const rect = selectedElement.getBoundingClientRect();
            const canvasRect = document.getElementById('canvas').getBoundingClientRect();
            
            const clientX = e.clientX || e.touches[0].clientX;
            const clientY = e.clientY || e.touches[0].clientY;
            
            dragOffset.x = clientX - rect.left;
            dragOffset.y = clientY - rect.top;
            
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', stopDrag);
            document.addEventListener('touchmove', drag);
            document.addEventListener('touchend', stopDrag);
        }

        function drag(e) {
            if (!isDragging || !selectedElement) return;
            
            e.preventDefault();
            const canvasRect = document.getElementById('canvas').getBoundingClientRect();
            
            const clientX = e.clientX || e.touches[0].clientX;
            const clientY = e.clientY || e.touches[0].clientY;
            
            let newX = clientX - canvasRect.left - dragOffset.x;
            let newY = clientY - canvasRect.top - dragOffset.y;
            
            // 邊界檢查
            newX = Math.max(0, Math.min(newX, canvasRect.width - selectedElement.offsetWidth));
            newY = Math.max(0, Math.min(newY, canvasRect.height - selectedElement.offsetHeight));
            
            selectedElement.style.left = newX + 'px';
            selectedElement.style.top = newY + 'px';
        }

        function stopDrag() {
            if (selectedElement) {
                selectedElement.classList.remove('dragging');
            }
            isDragging = false;
            selectedElement = null;
            
            document.removeEventListener('mousemove', drag);
            document.removeEventListener('mouseup', stopDrag);
            document.removeEventListener('touchmove', drag);
            document.removeEventListener('touchend', stopDrag);
        }

        // 初始化拖拽功能
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.vision-element').forEach(makeDraggable);
            
            // 頁面加載動畫
            const animateElements = document.querySelectorAll('.fade-in');
            animateElements.forEach((element, index) => {
                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
