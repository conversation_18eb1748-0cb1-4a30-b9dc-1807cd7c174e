<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>感恩願景 - 主畫面</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            position: relative;
        }

        .header {
            background: linear-gradient(135deg, #FF6B6B, #4ECDC4, #45B7D1);
            padding: 50px 20px 30px;
            text-align: center;
            position: relative;
        }

        .greeting {
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .date-info {
            color: white;
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .question {
            color: white;
            font-size: 18px;
            font-weight: 500;
            opacity: 0.95;
        }

        .add-button {
            position: absolute;
            top: 50px;
            right: 20px;
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 20px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .add-button:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        .add-button .material-icons {
            font-size: 24px;
        }

        .quick-stats {
            background: white;
            margin: -20px 20px 0;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            position: relative;
            z-index: 2;
        }

        .stat-item {
            text-align: center;
            flex: 1;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #FF6B6B;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #8E8E93;
        }

        .content {
            padding: 30px 20px 100px;
            background: #F8F9FA;
        }

        .modules-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 20px;
        }

        .module-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .module-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .module-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--module-color);
        }

        .module-icon {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            background: var(--module-color);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            color: white;
        }

        .module-icon .material-icons {
            font-size: 24px;
        }

        .module-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .module-description {
            font-size: 12px;
            color: #8E8E93;
            line-height: 16px;
        }

        .recent-activity {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .activity-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }

        .activity-title .material-icons {
            color: #FF6B6B;
            margin-right: 8px;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #F2F2F7;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: var(--activity-color);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
        }

        .activity-icon .material-icons {
            font-size: 16px;
        }

        .activity-content {
            flex: 1;
        }

        .activity-text {
            font-size: 14px;
            color: #333;
            margin-bottom: 2px;
        }

        .activity-time {
            font-size: 12px;
            color: #8E8E93;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #E5E5EA;
            padding: 8px 0;
            display: flex;
            justify-content: space-around;
            z-index: 100;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #8E8E93;
        }

        .nav-item.active {
            color: #FF6B6B;
        }

        .nav-item .material-icons {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .nav-text {
            font-size: 12px;
            font-weight: 500;
        }

        .add-menu {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .add-menu.show {
            display: flex;
        }

        .add-menu-content {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 20px;
            text-align: center;
            max-width: 300px;
            width: 100%;
        }

        .add-menu-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
        }

        .add-options {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
        }

        .add-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
            margin: 0 8px;
        }

        .add-option:hover {
            background: #F8F9FA;
        }

        .add-option-icon {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            background: var(--option-color);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            color: white;
        }

        .add-option-text {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .close-menu {
            background: #F2F2F7;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            color: #8E8E93;
            cursor: pointer;
            font-size: 16px;
        }

        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 頂部問候區域 -->
        <div class="header">
            <div class="greeting">早安，親愛的</div>
            <div class="date-info" id="currentDate">2024年1月20日 星期六</div>
            <div class="question">今天有什麼值得感恩的事情？</div>
            <button class="add-button" onclick="showAddMenu()">
                <span class="material-icons">add</span>
            </button>
        </div>

        <!-- 快速統計 -->
        <div class="quick-stats fade-in">
            <div class="stat-item">
                <div class="stat-number">15</div>
                <div class="stat-label">連續天數</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">156</div>
                <div class="stat-label">感恩記錄</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">5</div>
                <div class="stat-label">願景進度</div>
            </div>
        </div>

        <!-- 主要內容 -->
        <div class="content">
            <!-- 功能模組網格 -->
            <div class="modules-grid">
                <div class="module-card fade-in" style="--module-color: #FF6B6B;" onclick="location.href='index.html'">
                    <div class="module-icon">
                        <span class="material-icons">analytics</span>
                    </div>
                    <div class="module-title">感恩統計</div>
                    <div class="module-description">查看你的感恩成長軌跡</div>
                </div>

                <div class="module-card fade-in" style="--module-color: #4ECDC4;" onclick="location.href='diary.html'">
                    <div class="module-icon">
                        <span class="material-icons">book</span>
                    </div>
                    <div class="module-title">感恩日記</div>
                    <div class="module-description">記錄生活中的美好時刻</div>
                </div>

                <div class="module-card fade-in" style="--module-color: #45B7D1;" onclick="location.href='vision-board.html'">
                    <div class="module-icon">
                        <span class="material-icons">dashboard</span>
                    </div>
                    <div class="module-title">願景板</div>
                    <div class="module-description">創建和管理你的夢想</div>
                </div>

                <div class="module-card fade-in" style="--module-color: #96CEB4;" onclick="location.href='#'">
                    <div class="module-icon">
                        <span class="material-icons">self_improvement</span>
                    </div>
                    <div class="module-title">冥想引導</div>
                    <div class="module-description">沉靜心靈的冥想練習</div>
                </div>

                <div class="module-card fade-in" style="--module-color: #FFEAA7;" onclick="location.href='#'">
                    <div class="module-icon">
                        <span class="material-icons">card_giftcard</span>
                    </div>
                    <div class="module-title">感謝卡</div>
                    <div class="module-description">分享感謝與愛意</div>
                </div>

                <div class="module-card fade-in" style="--module-color: #DDA0DD;" onclick="location.href='#'">
                    <div class="module-icon">
                        <span class="material-icons">psychology</span>
                    </div>
                    <div class="module-title">能量天使</div>
                    <div class="module-description">溫柔的正念提醒</div>
                </div>
            </div>

            <!-- 最近活動 -->
            <div class="recent-activity fade-in">
                <div class="activity-title">
                    <span class="material-icons">history</span>
                    最近活動
                </div>
                
                <div class="activity-item">
                    <div class="activity-icon" style="--activity-color: #FF6B6B;">
                        <span class="material-icons">favorite</span>
                    </div>
                    <div class="activity-content">
                        <div class="activity-text">記錄了晨跑的感恩日記</div>
                        <div class="activity-time">2小時前</div>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon" style="--activity-color: #4ECDC4;">
                        <span class="material-icons">local_florist</span>
                    </div>
                    <div class="activity-content">
                        <div class="activity-text">為健康生活願景澆水</div>
                        <div class="activity-time">5小時前</div>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon" style="--activity-color: #45B7D1;">
                        <span class="material-icons">self_improvement</span>
                    </div>
                    <div class="activity-content">
                        <div class="activity-text">完成10分鐘感恩冥想</div>
                        <div class="activity-time">昨天</div>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon" style="--activity-color: #96CEB4;">
                        <span class="material-icons">card_giftcard</span>
                    </div>
                    <div class="activity-content">
                        <div class="activity-text">發送感謝卡給媽媽</div>
                        <div class="activity-time">2天前</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部導航 -->
        <div class="bottom-nav">
            <a href="#" class="nav-item active">
                <span class="material-icons">home</span>
                <span class="nav-text">首頁</span>
            </a>
            <a href="diary.html" class="nav-item">
                <span class="material-icons">book</span>
                <span class="nav-text">日記</span>
            </a>
            <a href="vision-board.html" class="nav-item">
                <span class="material-icons">dashboard</span>
                <span class="nav-text">願景板</span>
            </a>
            <a href="statistics.html" class="nav-item">
                <span class="material-icons">analytics</span>
                <span class="nav-text">統計</span>
            </a>
            <a href="profile.html" class="nav-item">
                <span class="material-icons">person</span>
                <span class="nav-text">個人</span>
            </a>
        </div>

        <!-- 新建選項菜單 -->
        <div class="add-menu" id="addMenu">
            <div class="add-menu-content">
                <div class="add-menu-title">新建記錄</div>
                <div class="add-options">
                    <div class="add-option" style="--option-color: #FF6B6B;" onclick="createDiary('text')">
                        <div class="add-option-icon">
                            <span class="material-icons">edit</span>
                        </div>
                        <div class="add-option-text">文字</div>
                    </div>
                    <div class="add-option" style="--option-color: #4ECDC4;" onclick="createDiary('image')">
                        <div class="add-option-icon">
                            <span class="material-icons">photo_camera</span>
                        </div>
                        <div class="add-option-text">圖片</div>
                    </div>
                    <div class="add-option" style="--option-color: #45B7D1;" onclick="createDiary('voice')">
                        <div class="add-option-icon">
                            <span class="material-icons">mic</span>
                        </div>
                        <div class="add-option-text">語音</div>
                    </div>
                </div>
                <button class="close-menu" onclick="hideAddMenu()">取消</button>
            </div>
        </div>
    </div>

    <script>
        // 顯示新建菜單
        function showAddMenu() {
            document.getElementById('addMenu').classList.add('show');
        }

        // 隱藏新建菜單
        function hideAddMenu() {
            document.getElementById('addMenu').classList.remove('show');
        }

        // 創建日記
        function createDiary(type) {
            hideAddMenu();
            alert(`創建${type === 'text' ? '文字' : type === 'image' ? '圖片' : '語音'}日記`);
        }

        // 點擊菜單外部關閉
        document.getElementById('addMenu').addEventListener('click', function(e) {
            if (e.target === this) {
                hideAddMenu();
            }
        });

        // 設置當前日期
        function setCurrentDate() {
            const now = new Date();
            const options = { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric', 
                weekday: 'long' 
            };
            const dateString = now.toLocaleDateString('zh-TW', options);
            document.getElementById('currentDate').textContent = dateString;
        }

        // 頁面加載動畫
        document.addEventListener('DOMContentLoaded', function() {
            setCurrentDate();
            
            const animateElements = document.querySelectorAll('.fade-in');
            animateElements.forEach((element, index) => {
                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // 添加脈衝效果到統計數字
            setTimeout(() => {
                document.querySelectorAll('.stat-number').forEach(el => {
                    el.classList.add('pulse');
                });
            }, 1000);
        });
    </script>
</body>
</html>
