<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>願景感恩日記 - 感恩與願景聯動模組預覽</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            background: #F8F9FA;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #FF6B6B, #4ECDC4, #45B7D1);
            padding: 50px 20px 20px;
            border-radius: 0 0 25px 25px;
            text-align: center;
        }

        .header h1 {
            color: white;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .header p {
            color: white;
            font-size: 16px;
            opacity: 0.9;
        }

        .tab-container {
            background: white;
            padding: 15px 0;
            border-bottom: 1px solid #E5E5EA;
            overflow-x: auto;
        }

        .tab-scroll {
            display: flex;
            padding: 0 20px;
            gap: 12px;
        }

        .tab-button {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 20px;
            background: #F2F2F7;
            color: #8E8E93;
            text-decoration: none;
            white-space: nowrap;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            background: #FF6B6B;
            color: white;
        }

        .tab-button .material-icons {
            font-size: 20px;
            margin-right: 6px;
        }

        .content {
            padding: 20px;
        }

        .dashboard-section {
            margin-bottom: 20px;
        }

        .progress-card {
            background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
            border-radius: 16px;
            padding: 20px;
            color: white;
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .progress-row {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .circular-progress {
            position: relative;
            width: 120px;
            height: 120px;
        }

        .progress-circle {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: conic-gradient(white 0deg 245deg, rgba(255,255,255,0.3) 245deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .progress-circle::before {
            content: '';
            position: absolute;
            width: 80%;
            height: 80%;
            background: #FF6B6B;
            border-radius: 50%;
        }

        .progress-inner {
            position: absolute;
            text-align: center;
            z-index: 1;
        }

        .streak-number {
            font-size: 32px;
            font-weight: bold;
            color: white;
        }

        .streak-label {
            font-size: 12px;
            color: white;
            opacity: 0.8;
        }

        .progress-info h3 {
            font-size: 16px;
            margin-bottom: 5px;
        }

        .progress-percentage {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .progress-subtext {
            font-size: 14px;
            opacity: 0.8;
        }

        .chart-section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .chart-section h3 {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }

        .emotion-chart {
            display: flex;
            justify-content: space-between;
            align-items: end;
            height: 150px;
            margin: 20px 0;
        }

        .emotion-bar {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 2px;
        }

        .emotion-bar-fill {
            width: 100%;
            background: linear-gradient(to top, #FF6B6B, #FF8E8E);
            border-radius: 4px 4px 0 0;
            margin-bottom: 8px;
            transition: height 0.8s ease;
        }

        .emotion-label {
            font-size: 12px;
            color: #8E8E93;
        }

        .achievement-section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .achievement-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #F2F2F7;
        }

        .achievement-item:last-child {
            border-bottom: none;
        }

        .achievement-icon {
            margin-right: 12px;
            color: #4CAF50;
        }

        .achievement-content {
            flex: 1;
        }

        .achievement-title {
            font-size: 16px;
            color: #333;
            margin-bottom: 4px;
        }

        .achievement-title.completed {
            text-decoration: line-through;
            color: #8E8E93;
        }

        .progress-bar {
            width: 200px;
            height: 4px;
            background: #E5E5EA;
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-bar-fill {
            height: 100%;
            background: #FF6B6B;
            border-radius: 2px;
            transition: width 0.8s ease;
        }

        .completed-badge {
            background: #4CAF50;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 頂部漸變背景 -->
        <div class="header">
            <h1>感恩與願景聯動</h1>
            <p>追蹤你的成長軌跡</p>
        </div>

        <!-- 標籤導航 -->
        <div class="tab-container">
            <div class="tab-scroll">
                <a href="#" class="tab-button active">
                    <span class="material-icons">dashboard</span>
                    儀表板
                </a>
                <a href="progress-tracker.html" class="tab-button">
                    <span class="material-icons">trending_up</span>
                    進度追蹤
                </a>
                <a href="gratitude-garden.html" class="tab-button">
                    <span class="material-icons">local_florist</span>
                    感恩花園
                </a>
                <a href="smart-recommendation.html" class="tab-button">
                    <span class="material-icons">lightbulb</span>
                    智能推薦
                </a>
                <a href="growth-review.html" class="tab-button">
                    <span class="material-icons">movie</span>
                    成長回顧
                </a>
            </div>
        </div>

        <!-- 內容區域 -->
        <div class="content">
            <!-- 環形進度圖區域 -->
            <div class="dashboard-section fade-in">
                <div class="progress-card">
                    <div class="section-title">記錄進度</div>
                    <div class="progress-row">
                        <div class="circular-progress pulse">
                            <div class="progress-circle">
                                <div class="progress-inner">
                                    <div class="streak-number">15</div>
                                    <div class="streak-label">連續天數</div>
                                </div>
                            </div>
                        </div>
                        <div class="progress-info">
                            <h3>本月完成度</h3>
                            <div class="progress-percentage">68%</div>
                            <div class="progress-subtext">已記錄 20 天</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 情感分布圖 -->
            <div class="chart-section fade-in">
                <h3>情感分布</h3>
                <div class="emotion-chart">
                    <div class="emotion-bar">
                        <div class="emotion-bar-fill" style="height: 85%;"></div>
                        <div class="emotion-label">喜悅</div>
                    </div>
                    <div class="emotion-bar">
                        <div class="emotion-bar-fill" style="height: 72%;"></div>
                        <div class="emotion-label">平靜</div>
                    </div>
                    <div class="emotion-bar">
                        <div class="emotion-bar-fill" style="height: 68%;"></div>
                        <div class="emotion-label">感動</div>
                    </div>
                    <div class="emotion-bar">
                        <div class="emotion-bar-fill" style="height: 91%;"></div>
                        <div class="emotion-label">希望</div>
                    </div>
                    <div class="emotion-bar">
                        <div class="emotion-bar-fill" style="height: 76%;"></div>
                        <div class="emotion-label">愛</div>
                    </div>
                    <div class="emotion-bar">
                        <div class="emotion-bar-fill" style="height: 82%;"></div>
                        <div class="emotion-label">滿足</div>
                    </div>
                </div>
            </div>

            <!-- 感恩成就 -->
            <div class="achievement-section fade-in">
                <h3>感恩成就</h3>
                <div class="achievement-item">
                    <span class="material-icons achievement-icon">check_circle</span>
                    <div class="achievement-content">
                        <div class="achievement-title completed">連續記錄7天</div>
                    </div>
                    <div class="completed-badge">已完成</div>
                </div>
                <div class="achievement-item">
                    <span class="material-icons achievement-icon">check_circle</span>
                    <div class="achievement-content">
                        <div class="achievement-title completed">收集30種積極情感</div>
                    </div>
                    <div class="completed-badge">已完成</div>
                </div>
                <div class="achievement-item">
                    <span class="material-icons achievement-icon" style="color: #E5E5EA;">radio_button_unchecked</span>
                    <div class="achievement-content">
                        <div class="achievement-title">連續記錄30天</div>
                        <div class="progress-bar">
                            <div class="progress-bar-fill" style="width: 50%;"></div>
                        </div>
                    </div>
                </div>
                <div class="achievement-item">
                    <span class="material-icons achievement-icon" style="color: #E5E5EA;">radio_button_unchecked</span>
                    <div class="achievement-content">
                        <div class="achievement-title">分享10次感謝卡</div>
                        <div class="progress-bar">
                            <div class="progress-bar-fill" style="width: 30%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加一些簡單的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 標籤切換效果
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 動畫效果
            const animateElements = document.querySelectorAll('.fade-in');
            animateElements.forEach((element, index) => {
                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 200);
            });

            // 進度條動畫
            setTimeout(() => {
                const progressBars = document.querySelectorAll('.progress-bar-fill');
                progressBars.forEach(bar => {
                    const width = bar.style.width;
                    bar.style.width = '0%';
                    setTimeout(() => {
                        bar.style.width = width;
                    }, 100);
                });

                const emotionBars = document.querySelectorAll('.emotion-bar-fill');
                emotionBars.forEach(bar => {
                    const height = bar.style.height;
                    bar.style.height = '0%';
                    setTimeout(() => {
                        bar.style.height = height;
                    }, 100);
                });
            }, 1000);
        });
    </script>
</body>
</html>
