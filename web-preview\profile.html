<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>個人中心 - 感恩願景App</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            background: #F8F9FA;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            position: relative;
        }

        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            padding: 50px 20px 30px;
            text-align: center;
            position: relative;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 40px;
            background: linear-gradient(135deg, #FF6B6B, #4ECDC4);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: white;
            font-size: 32px;
            font-weight: bold;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .profile-avatar:hover {
            transform: scale(1.05);
        }

        .avatar-edit {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #667eea;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .avatar-edit .material-icons {
            font-size: 16px;
        }

        .profile-name {
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .profile-subtitle {
            color: white;
            font-size: 16px;
            opacity: 0.9;
        }

        .stats-row {
            background: white;
            margin: -20px 20px 0;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            z-index: 2;
            display: flex;
            justify-content: space-around;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #8E8E93;
        }

        .content {
            padding: 30px 20px 100px;
            background: #F8F9FA;
        }

        .section {
            background: white;
            border-radius: 16px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .section-header {
            padding: 20px 20px 0;
            font-size: 18px;
            font-weight: bold;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-header .material-icons {
            color: #667eea;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 1px solid #F2F2F7;
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .menu-item:hover {
            background: #F8F9FA;
        }

        .menu-icon {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            background: var(--icon-color);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            color: white;
        }

        .menu-icon .material-icons {
            font-size: 20px;
        }

        .menu-content {
            flex: 1;
        }

        .menu-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }

        .menu-subtitle {
            font-size: 12px;
            color: #8E8E93;
        }

        .menu-arrow {
            color: #C7C7CC;
        }

        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: #E5E5EA;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-switch.active {
            background: #4CAF50;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .toggle-switch.active::before {
            transform: translateX(20px);
        }

        .achievement-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            padding: 0 20px 20px;
        }

        .achievement-item {
            text-align: center;
            padding: 16px;
            border-radius: 12px;
            background: #F8F9FA;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .achievement-item.unlocked {
            background: linear-gradient(135deg, var(--achievement-color), var(--achievement-color-light));
            color: white;
        }

        .achievement-item:hover {
            transform: translateY(-2px);
        }

        .achievement-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }

        .achievement-title {
            font-size: 12px;
            font-weight: 500;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #E5E5EA;
            padding: 8px 0;
            display: flex;
            justify-content: space-around;
            z-index: 100;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #8E8E93;
        }

        .nav-item.active {
            color: #FF6B6B;
        }

        .nav-item .material-icons {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .nav-text {
            font-size: 12px;
            font-weight: 500;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 20px;
            text-align: center;
            max-width: 300px;
            width: 100%;
        }

        .modal-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 16px;
        }

        .modal-text {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
            line-height: 20px;
        }

        .modal-buttons {
            display: flex;
            gap: 12px;
        }

        .modal-button {
            flex: 1;
            padding: 12px;
            border-radius: 12px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .modal-button.primary {
            background: #667eea;
            color: white;
        }

        .modal-button.secondary {
            background: #F2F2F7;
            color: #8E8E93;
        }

        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 頂部個人信息 -->
        <div class="header">
            <div class="profile-avatar" onclick="changeAvatar()">
                親
                <div class="avatar-edit">
                    <span class="material-icons">edit</span>
                </div>
            </div>
            <div class="profile-name">親愛的用戶</div>
            <div class="profile-subtitle">感恩之旅已進行 45 天</div>
        </div>

        <!-- 統計數據 -->
        <div class="stats-row fade-in">
            <div class="stat-item">
                <div class="stat-number">156</div>
                <div class="stat-label">感恩記錄</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">5</div>
                <div class="stat-label">願景板</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">12</div>
                <div class="stat-label">成就徽章</div>
            </div>
        </div>

        <!-- 主要內容 -->
        <div class="content">
            <!-- 成就徽章 -->
            <div class="section fade-in">
                <div class="section-header">
                    <span class="material-icons">emoji_events</span>
                    成就徽章
                </div>
                <div class="achievement-grid">
                    <div class="achievement-item unlocked" style="--achievement-color: #FF6B6B; --achievement-color-light: #FF8E8E;">
                        <div class="achievement-icon">🏃</div>
                        <div class="achievement-title">健康達人</div>
                    </div>
                    <div class="achievement-item unlocked" style="--achievement-color: #4ECDC4; --achievement-color-light: #6DD4CB;">
                        <div class="achievement-icon">📚</div>
                        <div class="achievement-title">學習之星</div>
                    </div>
                    <div class="achievement-item unlocked" style="--achievement-color: #45B7D1; --achievement-color-light: #6BC5D8;">
                        <div class="achievement-icon">❤️</div>
                        <div class="achievement-title">愛心使者</div>
                    </div>
                    <div class="achievement-item">
                        <div class="achievement-icon">🌟</div>
                        <div class="achievement-title">感恩大師</div>
                    </div>
                    <div class="achievement-item">
                        <div class="achievement-icon">🎯</div>
                        <div class="achievement-title">目標達成</div>
                    </div>
                    <div class="achievement-item">
                        <div class="achievement-icon">🔥</div>
                        <div class="achievement-title">連續記錄</div>
                    </div>
                </div>
            </div>

            <!-- 個人設置 -->
            <div class="section fade-in">
                <div class="section-header">
                    <span class="material-icons">settings</span>
                    個人設置
                </div>
                <div class="menu-item" onclick="editProfile()">
                    <div class="menu-icon" style="--icon-color: #FF6B6B;">
                        <span class="material-icons">person</span>
                    </div>
                    <div class="menu-content">
                        <div class="menu-title">編輯個人資料</div>
                        <div class="menu-subtitle">修改姓名、頭像等信息</div>
                    </div>
                    <span class="material-icons menu-arrow">chevron_right</span>
                </div>
                <div class="menu-item" onclick="notificationSettings()">
                    <div class="menu-icon" style="--icon-color: #4ECDC4;">
                        <span class="material-icons">notifications</span>
                    </div>
                    <div class="menu-content">
                        <div class="menu-title">通知設置</div>
                        <div class="menu-subtitle">管理提醒和通知偏好</div>
                    </div>
                    <div class="toggle-switch active" onclick="toggleNotification(event)"></div>
                </div>
                <div class="menu-item" onclick="privacySettings()">
                    <div class="menu-icon" style="--icon-color: #45B7D1;">
                        <span class="material-icons">security</span>
                    </div>
                    <div class="menu-content">
                        <div class="menu-title">隱私設置</div>
                        <div class="menu-subtitle">數據安全和隱私保護</div>
                    </div>
                    <span class="material-icons menu-arrow">chevron_right</span>
                </div>
            </div>

            <!-- 應用偏好 -->
            <div class="section fade-in">
                <div class="section-header">
                    <span class="material-icons">tune</span>
                    應用偏好
                </div>
                <div class="menu-item" onclick="themeSettings()">
                    <div class="menu-icon" style="--icon-color: #96CEB4;">
                        <span class="material-icons">palette</span>
                    </div>
                    <div class="menu-content">
                        <div class="menu-title">主題設置</div>
                        <div class="menu-subtitle">選擇你喜歡的外觀主題</div>
                    </div>
                    <span class="material-icons menu-arrow">chevron_right</span>
                </div>
                <div class="menu-item" onclick="reminderSettings()">
                    <div class="menu-icon" style="--icon-color: #FFEAA7;">
                        <span class="material-icons">schedule</span>
                    </div>
                    <div class="menu-content">
                        <div class="menu-title">提醒設置</div>
                        <div class="menu-subtitle">設置感恩記錄提醒時間</div>
                    </div>
                    <span class="material-icons menu-arrow">chevron_right</span>
                </div>
                <div class="menu-item" onclick="languageSettings()">
                    <div class="menu-icon" style="--icon-color: #DDA0DD;">
                        <span class="material-icons">language</span>
                    </div>
                    <div class="menu-content">
                        <div class="menu-title">語言設置</div>
                        <div class="menu-subtitle">繁體中文</div>
                    </div>
                    <span class="material-icons menu-arrow">chevron_right</span>
                </div>
            </div>

            <!-- 數據管理 -->
            <div class="section fade-in">
                <div class="section-header">
                    <span class="material-icons">storage</span>
                    數據管理
                </div>
                <div class="menu-item" onclick="exportData()">
                    <div class="menu-icon" style="--icon-color: #4CAF50;">
                        <span class="material-icons">download</span>
                    </div>
                    <div class="menu-content">
                        <div class="menu-title">導出數據</div>
                        <div class="menu-subtitle">備份你的感恩記錄</div>
                    </div>
                    <span class="material-icons menu-arrow">chevron_right</span>
                </div>
                <div class="menu-item" onclick="syncSettings()">
                    <div class="menu-icon" style="--icon-color: #2196F3;">
                        <span class="material-icons">sync</span>
                    </div>
                    <div class="menu-content">
                        <div class="menu-title">雲端同步</div>
                        <div class="menu-subtitle">跨設備同步數據</div>
                    </div>
                    <div class="toggle-switch" onclick="toggleSync(event)"></div>
                </div>
                <div class="menu-item" onclick="clearData()">
                    <div class="menu-icon" style="--icon-color: #F44336;">
                        <span class="material-icons">delete</span>
                    </div>
                    <div class="menu-content">
                        <div class="menu-title">清除數據</div>
                        <div class="menu-subtitle">刪除所有本地數據</div>
                    </div>
                    <span class="material-icons menu-arrow">chevron_right</span>
                </div>
            </div>

            <!-- 幫助與支持 -->
            <div class="section fade-in">
                <div class="section-header">
                    <span class="material-icons">help</span>
                    幫助與支持
                </div>
                <div class="menu-item" onclick="helpCenter()">
                    <div class="menu-icon" style="--icon-color: #FF9800;">
                        <span class="material-icons">help_center</span>
                    </div>
                    <div class="menu-content">
                        <div class="menu-title">幫助中心</div>
                        <div class="menu-subtitle">常見問題和使用指南</div>
                    </div>
                    <span class="material-icons menu-arrow">chevron_right</span>
                </div>
                <div class="menu-item" onclick="feedback()">
                    <div class="menu-icon" style="--icon-color: #9C27B0;">
                        <span class="material-icons">feedback</span>
                    </div>
                    <div class="menu-content">
                        <div class="menu-title">意見反饋</div>
                        <div class="menu-subtitle">告訴我們你的想法</div>
                    </div>
                    <span class="material-icons menu-arrow">chevron_right</span>
                </div>
                <div class="menu-item" onclick="about()">
                    <div class="menu-icon" style="--icon-color: #607D8B;">
                        <span class="material-icons">info</span>
                    </div>
                    <div class="menu-content">
                        <div class="menu-title">關於我們</div>
                        <div class="menu-subtitle">版本 1.0.0</div>
                    </div>
                    <span class="material-icons menu-arrow">chevron_right</span>
                </div>
            </div>
        </div>

        <!-- 底部導航 -->
        <div class="bottom-nav">
            <a href="main-app.html" class="nav-item">
                <span class="material-icons">home</span>
                <span class="nav-text">首頁</span>
            </a>
            <a href="diary.html" class="nav-item">
                <span class="material-icons">book</span>
                <span class="nav-text">日記</span>
            </a>
            <a href="vision-board.html" class="nav-item">
                <span class="material-icons">dashboard</span>
                <span class="nav-text">願景板</span>
            </a>
            <a href="statistics.html" class="nav-item">
                <span class="material-icons">analytics</span>
                <span class="nav-text">統計</span>
            </a>
            <a href="#" class="nav-item active">
                <span class="material-icons">person</span>
                <span class="nav-text">個人</span>
            </a>
        </div>

        <!-- 確認對話框 -->
        <div class="modal" id="confirmModal">
            <div class="modal-content">
                <div class="modal-title" id="modalTitle">確認操作</div>
                <div class="modal-text" id="modalText">你確定要執行此操作嗎？</div>
                <div class="modal-buttons">
                    <button class="modal-button secondary" onclick="hideModal()">取消</button>
                    <button class="modal-button primary" onclick="confirmAction()">確認</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentAction = null;

        // 更換頭像
        function changeAvatar() {
            alert('打開頭像選擇器');
        }

        // 編輯個人資料
        function editProfile() {
            alert('打開個人資料編輯頁面');
        }

        // 通知設置
        function notificationSettings() {
            alert('打開通知設置頁面');
        }

        // 切換通知開關
        function toggleNotification(event) {
            event.stopPropagation();
            const toggle = event.target;
            toggle.classList.toggle('active');
        }

        // 隱私設置
        function privacySettings() {
            alert('打開隱私設置頁面');
        }

        // 主題設置
        function themeSettings() {
            alert('打開主題設置頁面');
        }

        // 提醒設置
        function reminderSettings() {
            alert('打開提醒設置頁面');
        }

        // 語言設置
        function languageSettings() {
            alert('打開語言設置頁面');
        }

        // 導出數據
        function exportData() {
            showModal('導出數據', '將會導出你的所有感恩記錄和願景數據，是否繼續？', 'export');
        }

        // 切換同步開關
        function toggleSync(event) {
            event.stopPropagation();
            const toggle = event.target;
            toggle.classList.toggle('active');
            
            if (toggle.classList.contains('active')) {
                alert('雲端同步已開啟');
            } else {
                alert('雲端同步已關閉');
            }
        }

        // 清除數據
        function clearData() {
            showModal('清除數據', '此操作將刪除所有本地數據且無法恢復，請確認是否繼續？', 'clear');
        }

        // 幫助中心
        function helpCenter() {
            alert('打開幫助中心');
        }

        // 意見反饋
        function feedback() {
            alert('打開意見反饋頁面');
        }

        // 關於我們
        function about() {
            alert('感恩願景 App v1.0.0\n讓感恩成為生活的習慣');
        }

        // 顯示確認對話框
        function showModal(title, text, action) {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalText').textContent = text;
            document.getElementById('confirmModal').classList.add('show');
            currentAction = action;
        }

        // 隱藏對話框
        function hideModal() {
            document.getElementById('confirmModal').classList.remove('show');
            currentAction = null;
        }

        // 確認操作
        function confirmAction() {
            if (currentAction === 'export') {
                alert('數據導出中...');
            } else if (currentAction === 'clear') {
                alert('數據已清除');
            }
            hideModal();
        }

        // 點擊模態框外部關閉
        document.getElementById('confirmModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideModal();
            }
        });

        // 頁面加載動畫
        document.addEventListener('DOMContentLoaded', function() {
            const animateElements = document.querySelectorAll('.fade-in');
            animateElements.forEach((element, index) => {
                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
