<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>感恩日記 - 感恩願景App</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            background: #F8F9FA;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            position: relative;
        }

        .header {
            background: linear-gradient(135deg, #FF6B6B, #4ECDC4);
            padding: 50px 20px 20px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .header p {
            color: white;
            font-size: 16px;
            opacity: 0.9;
        }

        .add-button {
            position: absolute;
            top: 50px;
            right: 20px;
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 20px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .add-button:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        .content {
            padding: 20px;
            padding-bottom: 100px;
        }

        .month-navigator {
            position: fixed;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: white;
            border-radius: 20px;
            padding: 8px 4px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            z-index: 10;
        }

        .month-item {
            display: block;
            padding: 8px 12px;
            text-align: center;
            font-size: 12px;
            color: #8E8E93;
            cursor: pointer;
            border-radius: 12px;
            margin: 2px 0;
            transition: all 0.3s ease;
        }

        .month-item.active {
            background: #FF6B6B;
            color: white;
        }

        .diary-timeline {
            margin-right: 60px;
        }

        .diary-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .diary-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .diary-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .diary-date {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .diary-weekday {
            font-size: 12px;
            color: #8E8E93;
            margin-top: 2px;
        }

        .media-indicators {
            display: flex;
            gap: 8px;
        }

        .media-icon {
            width: 24px;
            height: 24px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        .media-text {
            background: #4ECDC4;
        }

        .media-image {
            background: #45B7D1;
        }

        .media-voice {
            background: #96CEB4;
        }

        .diary-content {
            font-size: 14px;
            color: #666;
            line-height: 20px;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .emotion-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .emotion-tag {
            background: var(--emotion-color);
            color: white;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #E5E5EA;
            padding: 8px 0;
            display: flex;
            justify-content: space-around;
            z-index: 100;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #8E8E93;
        }

        .nav-item.active {
            color: #FF6B6B;
        }

        .nav-item .material-icons {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .nav-text {
            font-size: 12px;
            font-weight: 500;
        }

        .create-diary-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: flex-end;
            z-index: 1000;
        }

        .create-diary-modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 20px 20px 0 0;
            width: 100%;
            max-width: 414px;
            max-height: 80vh;
            overflow-y: auto;
            animation: slideUp 0.3s ease;
        }

        @keyframes slideUp {
            from { transform: translateY(100%); }
            to { transform: translateY(0); }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #E5E5EA;
        }

        .modal-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
        }

        .close-button {
            padding: 4px;
            cursor: pointer;
            color: #8E8E93;
        }

        .modal-body {
            padding: 20px;
        }

        .input-section {
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }

        .text-input {
            width: 100%;
            min-height: 120px;
            padding: 16px;
            border: 1px solid #E5E5EA;
            border-radius: 12px;
            font-size: 16px;
            font-family: inherit;
            resize: vertical;
            background: #F8F9FA;
        }

        .text-input::placeholder {
            color: #8E8E93;
        }

        .media-options {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
        }

        .media-option {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px;
            border: 2px dashed #E5E5EA;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .media-option:hover {
            border-color: #FF6B6B;
            background: #FF6B6B10;
        }

        .media-option .material-icons {
            font-size: 32px;
            color: #8E8E93;
            margin-bottom: 8px;
        }

        .media-option-text {
            font-size: 14px;
            color: #8E8E93;
        }

        .emotion-selector {
            margin-bottom: 20px;
        }

        .emotion-wheel {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }

        .emotion-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .emotion-item:hover {
            background: #F8F9FA;
        }

        .emotion-item.selected {
            border-color: var(--emotion-color);
            background: var(--emotion-color-light);
        }

        .emotion-emoji {
            font-size: 32px;
            margin-bottom: 8px;
        }

        .emotion-name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .intensity-slider {
            margin-top: 16px;
        }

        .slider-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #E5E5EA;
            outline: none;
            -webkit-appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #FF6B6B;
            cursor: pointer;
        }

        .save-button {
            width: 100%;
            background: #FF6B6B;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .save-button:hover {
            background: #FF5252;
        }

        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 頂部標題 -->
        <div class="header">
            <h1>感恩日記</h1>
            <p>記錄生活中的美好時刻</p>
            <button class="add-button" onclick="showCreateModal()">
                <span class="material-icons">add</span>
            </button>
        </div>

        <!-- 月份導航 -->
        <div class="month-navigator">
            <div class="month-item active">1月</div>
            <div class="month-item">2月</div>
            <div class="month-item">3月</div>
            <div class="month-item">4月</div>
            <div class="month-item">5月</div>
            <div class="month-item">6月</div>
        </div>

        <!-- 主要內容 -->
        <div class="content">
            <div class="diary-timeline">
                <!-- 日記卡片1 -->
                <div class="diary-card fade-in" onclick="viewDiary(1)">
                    <div class="diary-header">
                        <div>
                            <div class="diary-date">1月20日</div>
                            <div class="diary-weekday">星期六</div>
                        </div>
                        <div class="media-indicators">
                            <div class="media-icon media-text">
                                <span class="material-icons">edit</span>
                            </div>
                            <div class="media-icon media-image">
                                <span class="material-icons">photo</span>
                            </div>
                        </div>
                    </div>
                    <div class="diary-content">
                        今天早上6點起床去公園跑步，呼吸新鮮空氣，感受陽光的溫暖。跑步過程中遇到了很多晨練的老人家，他們的活力讓我深受感動。感恩有健康的身體可以運動，感恩美好的天氣...
                    </div>
                    <div class="emotion-tags">
                        <span class="emotion-tag" style="--emotion-color: #FF6B6B;">喜悅</span>
                        <span class="emotion-tag" style="--emotion-color: #4ECDC4;">感動</span>
                        <span class="emotion-tag" style="--emotion-color: #45B7D1;">希望</span>
                    </div>
                </div>

                <!-- 日記卡片2 -->
                <div class="diary-card fade-in" onclick="viewDiary(2)">
                    <div class="diary-header">
                        <div>
                            <div class="diary-date">1月19日</div>
                            <div class="diary-weekday">星期五</div>
                        </div>
                        <div class="media-indicators">
                            <div class="media-icon media-voice">
                                <span class="material-icons">mic</span>
                            </div>
                        </div>
                    </div>
                    <div class="diary-content">
                        今天完成了重要的項目提案，經過一週的努力終於有了成果。團隊合作很愉快，每個人都貢獻了自己的專長。感恩有這樣的工作機會，感恩團隊夥伴的支持...
                    </div>
                    <div class="emotion-tags">
                        <span class="emotion-tag" style="--emotion-color: #96CEB4;">滿足</span>
                        <span class="emotion-tag" style="--emotion-color: #FFEAA7;">感恩</span>
                    </div>
                </div>

                <!-- 日記卡片3 -->
                <div class="diary-card fade-in" onclick="viewDiary(3)">
                    <div class="diary-header">
                        <div>
                            <div class="diary-date">1月18日</div>
                            <div class="diary-weekday">星期四</div>
                        </div>
                        <div class="media-indicators">
                            <div class="media-icon media-text">
                                <span class="material-icons">edit</span>
                            </div>
                        </div>
                    </div>
                    <div class="diary-content">
                        週末和父母一起做飯，聊天，感受到家庭的溫暖和愛。媽媽教我做她的拿手菜，爸爸分享他年輕時的故事。這樣的時光真的很珍貴...
                    </div>
                    <div class="emotion-tags">
                        <span class="emotion-tag" style="--emotion-color: #DDA0DD;">愛</span>
                        <span class="emotion-tag" style="--emotion-color: #FF6B6B;">溫暖</span>
                    </div>
                </div>

                <!-- 日記卡片4 -->
                <div class="diary-card fade-in" onclick="viewDiary(4)">
                    <div class="diary-header">
                        <div>
                            <div class="diary-date">1月17日</div>
                            <div class="diary-weekday">星期三</div>
                        </div>
                        <div class="media-indicators">
                            <div class="media-icon media-image">
                                <span class="material-icons">photo</span>
                            </div>
                            <div class="media-icon media-voice">
                                <span class="material-icons">mic</span>
                            </div>
                        </div>
                    </div>
                    <div class="diary-content">
                        今天在咖啡廳讀書時，看到一位老奶奶獨自坐在角落。我主動過去聊天，發現她很孤單。我們聊了很久，她分享了很多人生智慧。感恩這次相遇...
                    </div>
                    <div class="emotion-tags">
                        <span class="emotion-tag" style="--emotion-color: #4ECDC4;">感動</span>
                        <span class="emotion-tag" style="--emotion-color: #96CEB4;">平靜</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部導航 -->
        <div class="bottom-nav">
            <a href="main-app.html" class="nav-item">
                <span class="material-icons">home</span>
                <span class="nav-text">首頁</span>
            </a>
            <a href="#" class="nav-item active">
                <span class="material-icons">book</span>
                <span class="nav-text">日記</span>
            </a>
            <a href="#" class="nav-item">
                <span class="material-icons">dashboard</span>
                <span class="nav-text">願景板</span>
            </a>
            <a href="index.html" class="nav-item">
                <span class="material-icons">analytics</span>
                <span class="nav-text">統計</span>
            </a>
            <a href="#" class="nav-item">
                <span class="material-icons">person</span>
                <span class="nav-text">個人</span>
            </a>
        </div>

        <!-- 創建日記模態框 -->
        <div class="create-diary-modal" id="createModal">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title">新建感恩日記</div>
                    <div class="close-button" onclick="hideCreateModal()">
                        <span class="material-icons">close</span>
                    </div>
                </div>
                <div class="modal-body">
                    <!-- 文字輸入 -->
                    <div class="input-section">
                        <div class="section-title">今天我感恩...</div>
                        <textarea class="text-input" placeholder="分享今天讓你感恩的事情吧..."></textarea>
                    </div>

                    <!-- 媒體選項 -->
                    <div class="input-section">
                        <div class="section-title">添加媒體</div>
                        <div class="media-options">
                            <div class="media-option" onclick="addPhoto()">
                                <span class="material-icons">photo_camera</span>
                                <div class="media-option-text">拍照</div>
                            </div>
                            <div class="media-option" onclick="selectPhoto()">
                                <span class="material-icons">photo_library</span>
                                <div class="media-option-text">相冊</div>
                            </div>
                            <div class="media-option" onclick="recordVoice()">
                                <span class="material-icons">mic</span>
                                <div class="media-option-text">錄音</div>
                            </div>
                        </div>
                    </div>

                    <!-- 情感選擇 -->
                    <div class="input-section">
                        <div class="section-title">選擇情感</div>
                        <div class="emotion-wheel">
                            <div class="emotion-item" onclick="selectEmotion(this, '#FF6B6B')" style="--emotion-color: #FF6B6B; --emotion-color-light: #FF6B6B20;">
                                <div class="emotion-emoji">😊</div>
                                <div class="emotion-name">喜悅</div>
                            </div>
                            <div class="emotion-item" onclick="selectEmotion(this, '#4ECDC4')" style="--emotion-color: #4ECDC4; --emotion-color-light: #4ECDC420;">
                                <div class="emotion-emoji">😌</div>
                                <div class="emotion-name">平靜</div>
                            </div>
                            <div class="emotion-item" onclick="selectEmotion(this, '#45B7D1')" style="--emotion-color: #45B7D1; --emotion-color-light: #45B7D120;">
                                <div class="emotion-emoji">😢</div>
                                <div class="emotion-name">感動</div>
                            </div>
                            <div class="emotion-item" onclick="selectEmotion(this, '#96CEB4')" style="--emotion-color: #96CEB4; --emotion-color-light: #96CEB420;">
                                <div class="emotion-emoji">🤗</div>
                                <div class="emotion-name">希望</div>
                            </div>
                            <div class="emotion-item" onclick="selectEmotion(this, '#FFEAA7')" style="--emotion-color: #FFEAA7; --emotion-color-light: #FFEAA720;">
                                <div class="emotion-emoji">❤️</div>
                                <div class="emotion-name">愛</div>
                            </div>
                            <div class="emotion-item" onclick="selectEmotion(this, '#DDA0DD')" style="--emotion-color: #DDA0DD; --emotion-color-light: #DDA0DD20;">
                                <div class="emotion-emoji">😇</div>
                                <div class="emotion-name">感恩</div>
                            </div>
                        </div>
                        
                        <div class="intensity-slider">
                            <div class="slider-label">情感強度：<span id="intensityValue">3</span></div>
                            <input type="range" min="1" max="5" value="3" class="slider" id="intensitySlider" oninput="updateIntensity(this.value)">
                        </div>
                    </div>

                    <button class="save-button" onclick="saveDiary()">保存日記</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 顯示創建模態框
        function showCreateModal() {
            document.getElementById('createModal').classList.add('show');
        }

        // 隱藏創建模態框
        function hideCreateModal() {
            document.getElementById('createModal').classList.remove('show');
        }

        // 查看日記詳情
        function viewDiary(id) {
            alert(`查看日記 ${id} 的詳細內容`);
        }

        // 選擇情感
        function selectEmotion(element, color) {
            // 移除其他選中狀態
            document.querySelectorAll('.emotion-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 添加選中狀態
            element.classList.add('selected');
        }

        // 更新情感強度
        function updateIntensity(value) {
            document.getElementById('intensityValue').textContent = value;
        }

        // 媒體功能
        function addPhoto() {
            alert('打開相機拍照');
        }

        function selectPhoto() {
            alert('從相冊選擇照片');
        }

        function recordVoice() {
            alert('開始錄音');
        }

        // 保存日記
        function saveDiary() {
            const text = document.querySelector('.text-input').value;
            const selectedEmotion = document.querySelector('.emotion-item.selected');
            const intensity = document.getElementById('intensitySlider').value;
            
            if (!text.trim()) {
                alert('請輸入日記內容');
                return;
            }
            
            if (!selectedEmotion) {
                alert('請選擇情感標籤');
                return;
            }
            
            alert(`日記保存成功！\n內容：${text.substring(0, 50)}...\n情感：${selectedEmotion.querySelector('.emotion-name').textContent}\n強度：${intensity}`);
            hideCreateModal();
        }

        // 點擊模態框外部關閉
        document.getElementById('createModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideCreateModal();
            }
        });

        // 月份導航
        document.querySelectorAll('.month-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.month-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 頁面加載動畫
        document.addEventListener('DOMContentLoaded', function() {
            const animateElements = document.querySelectorAll('.fade-in');
            animateElements.forEach((element, index) => {
                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
