<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>統計分析 - 感恩願景App</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            background: #F8F9FA;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            position: relative;
        }

        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            padding: 50px 20px 20px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .header p {
            color: white;
            font-size: 16px;
            opacity: 0.9;
        }

        .time-selector {
            background: white;
            margin: -20px 20px 0;
            border-radius: 16px;
            padding: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            z-index: 2;
        }

        .time-tabs {
            display: flex;
            background: #F2F2F7;
            border-radius: 12px;
            padding: 4px;
        }

        .time-tab {
            flex: 1;
            text-align: center;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #8E8E93;
        }

        .time-tab.active {
            background: white;
            color: #667eea;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .content {
            padding: 30px 20px 100px;
            background: #F8F9FA;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color);
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: var(--card-color);
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #8E8E93;
            margin-bottom: 4px;
        }

        .stat-change {
            font-size: 12px;
            color: #4CAF50;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 2px;
        }

        .chart-section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .chart-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chart-title .material-icons {
            color: #667eea;
        }

        .emotion-radar {
            position: relative;
            width: 200px;
            height: 200px;
            margin: 20px auto;
        }

        .radar-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 2px solid #E5E5EA;
        }

        .radar-line {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 1px;
            height: 50%;
            background: #E5E5EA;
            transform-origin: bottom;
        }

        .emotion-point {
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--emotion-color);
            transform: translate(-50%, -50%);
        }

        .emotion-label {
            position: absolute;
            font-size: 12px;
            color: #666;
            transform: translate(-50%, -50%);
        }

        .trend-chart {
            height: 150px;
            background: linear-gradient(135deg, #667eea10, #764ba210);
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            margin: 16px 0;
        }

        .trend-line {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            clip-path: polygon(0% 100%, 10% 80%, 25% 85%, 40% 70%, 55% 75%, 70% 60%, 85% 65%, 100% 50%, 100% 100%);
        }

        .trend-points {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            align-items: end;
            justify-content: space-around;
            padding: 20px 10px;
        }

        .trend-point {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #667eea;
            position: relative;
        }

        .heatmap-container {
            margin: 16px 0;
        }

        .heatmap-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 4px;
            margin-bottom: 12px;
        }

        .heatmap-cell {
            aspect-ratio: 1;
            border-radius: 4px;
            background: var(--intensity-color);
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .heatmap-cell:hover {
            transform: scale(1.2);
        }

        .heatmap-legend {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #8E8E93;
        }

        .legend-scale {
            display: flex;
            gap: 2px;
        }

        .legend-item {
            width: 12px;
            height: 12px;
            border-radius: 2px;
        }

        .insights-section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .insight-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 16px;
            padding: 16px;
            background: #F8F9FA;
            border-radius: 12px;
        }

        .insight-icon {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: var(--insight-color);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
        }

        .insight-content {
            flex: 1;
        }

        .insight-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .insight-text {
            font-size: 12px;
            color: #666;
            line-height: 16px;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #E5E5EA;
            padding: 8px 0;
            display: flex;
            justify-content: space-around;
            z-index: 100;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #8E8E93;
        }

        .nav-item.active {
            color: #FF6B6B;
        }

        .nav-item .material-icons {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .nav-text {
            font-size: 12px;
            font-weight: 500;
        }

        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 頂部標題 -->
        <div class="header">
            <h1>統計分析</h1>
            <p>深入了解你的感恩成長軌跡</p>
        </div>

        <!-- 時間選擇器 -->
        <div class="time-selector fade-in">
            <div class="time-tabs">
                <div class="time-tab">本週</div>
                <div class="time-tab active">本月</div>
                <div class="time-tab">本年</div>
            </div>
        </div>

        <!-- 主要內容 -->
        <div class="content">
            <!-- 統計概覽 -->
            <div class="stats-overview">
                <div class="stat-card fade-in" style="--card-color: #FF6B6B;">
                    <div class="stat-number pulse">156</div>
                    <div class="stat-label">感恩記錄</div>
                    <div class="stat-change">
                        <span class="material-icons" style="font-size: 12px;">trending_up</span>
                        +12% 較上月
                    </div>
                </div>

                <div class="stat-card fade-in" style="--card-color: #4ECDC4;">
                    <div class="stat-number pulse">15</div>
                    <div class="stat-label">連續天數</div>
                    <div class="stat-change">
                        <span class="material-icons" style="font-size: 12px;">trending_up</span>
                        新紀錄！
                    </div>
                </div>

                <div class="stat-card fade-in" style="--card-color: #45B7D1;">
                    <div class="stat-number pulse">5</div>
                    <div class="stat-label">活躍願景</div>
                    <div class="stat-change">
                        <span class="material-icons" style="font-size: 12px;">trending_up</span>
                        +1 本月
                    </div>
                </div>

                <div class="stat-card fade-in" style="--card-color: #96CEB4;">
                    <div class="stat-number pulse">68%</div>
                    <div class="stat-label">平均完成度</div>
                    <div class="stat-change">
                        <span class="material-icons" style="font-size: 12px;">trending_up</span>
                        +8% 較上月
                    </div>
                </div>
            </div>

            <!-- 情感雷達圖 -->
            <div class="chart-section fade-in">
                <div class="chart-title">
                    <span class="material-icons">psychology</span>
                    情感分布雷達圖
                </div>
                <div class="emotion-radar">
                    <div class="radar-bg"></div>
                    <div class="radar-line" style="transform: rotate(0deg);"></div>
                    <div class="radar-line" style="transform: rotate(60deg);"></div>
                    <div class="radar-line" style="transform: rotate(120deg);"></div>
                    <div class="radar-line" style="transform: rotate(180deg);"></div>
                    <div class="radar-line" style="transform: rotate(240deg);"></div>
                    <div class="radar-line" style="transform: rotate(300deg);"></div>
                    
                    <!-- 情感點 -->
                    <div class="emotion-point" style="--emotion-color: #FF6B6B; top: 20%; left: 50%;"></div>
                    <div class="emotion-label" style="top: 15%; left: 50%;">喜悅</div>
                    
                    <div class="emotion-point" style="--emotion-color: #4ECDC4; top: 35%; left: 85%;"></div>
                    <div class="emotion-label" style="top: 30%; left: 90%;">平靜</div>
                    
                    <div class="emotion-point" style="--emotion-color: #45B7D1; top: 65%; left: 85%;"></div>
                    <div class="emotion-label" style="top: 70%; left: 90%;">感動</div>
                    
                    <div class="emotion-point" style="--emotion-color: #96CEB4; top: 80%; left: 50%;"></div>
                    <div class="emotion-label" style="top: 85%; left: 50%;">希望</div>
                    
                    <div class="emotion-point" style="--emotion-color: #FFEAA7; top: 65%; left: 15%;"></div>
                    <div class="emotion-label" style="top: 70%; left: 10%;">愛</div>
                    
                    <div class="emotion-point" style="--emotion-color: #DDA0DD; top: 35%; left: 15%;"></div>
                    <div class="emotion-label" style="top: 30%; left: 10%;">感恩</div>
                </div>
            </div>

            <!-- 情感變化趨勢 -->
            <div class="chart-section fade-in">
                <div class="chart-title">
                    <span class="material-icons">trending_up</span>
                    積極情感變化趨勢
                </div>
                <div class="trend-chart">
                    <div class="trend-line"></div>
                    <div class="trend-points">
                        <div class="trend-point" style="bottom: 40%;"></div>
                        <div class="trend-point" style="bottom: 50%;"></div>
                        <div class="trend-point" style="bottom: 45%;"></div>
                        <div class="trend-point" style="bottom: 60%;"></div>
                        <div class="trend-point" style="bottom: 65%;"></div>
                        <div class="trend-point" style="bottom: 70%;"></div>
                        <div class="trend-point" style="bottom: 75%;"></div>
                    </div>
                </div>
                <div style="display: flex; justify-content: space-between; font-size: 12px; color: #8E8E93; margin-top: 8px;">
                    <span>1週前</span>
                    <span>6天前</span>
                    <span>5天前</span>
                    <span>4天前</span>
                    <span>3天前</span>
                    <span>2天前</span>
                    <span>昨天</span>
                </div>
            </div>

            <!-- 記錄時間熱力圖 -->
            <div class="chart-section fade-in">
                <div class="chart-title">
                    <span class="material-icons">schedule</span>
                    記錄時間熱力圖
                </div>
                <div class="heatmap-container">
                    <div class="heatmap-grid">
                        <!-- 生成7x4的熱力圖網格 -->
                        <div class="heatmap-cell" style="--intensity-color: #E5E5EA;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B40;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B80;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B80;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B40;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #E5E5EA;"></div>
                        
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B40;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B80;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B80;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B40;"></div>
                        
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B80;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B80;"></div>
                        
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B40;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B80;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B80;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B80;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #FF6B6B40;"></div>
                        <div class="heatmap-cell" style="--intensity-color: #E5E5EA;"></div>
                    </div>
                    <div class="heatmap-legend">
                        <span>較少</span>
                        <div class="legend-scale">
                            <div class="legend-item" style="background: #E5E5EA;"></div>
                            <div class="legend-item" style="background: #FF6B6B40;"></div>
                            <div class="legend-item" style="background: #FF6B6B80;"></div>
                            <div class="legend-item" style="background: #FF6B6B;"></div>
                        </div>
                        <span>較多</span>
                    </div>
                </div>
                <div style="display: flex; justify-content: space-around; font-size: 12px; color: #8E8E93; margin-top: 12px;">
                    <span>6時</span>
                    <span>9時</span>
                    <span>12時</span>
                    <span>15時</span>
                    <span>18時</span>
                    <span>21時</span>
                    <span>24時</span>
                </div>
            </div>

            <!-- 智能洞察 -->
            <div class="insights-section fade-in">
                <div class="chart-title">
                    <span class="material-icons">lightbulb</span>
                    智能洞察
                </div>
                
                <div class="insight-item">
                    <div class="insight-icon" style="--insight-color: #4CAF50;">
                        <span class="material-icons" style="font-size: 16px;">trending_up</span>
                    </div>
                    <div class="insight-content">
                        <div class="insight-title">記錄習慣持續改善</div>
                        <div class="insight-text">你的記錄頻率比上月提升了12%，保持這個節奏將有助於建立穩定的感恩習慣。</div>
                    </div>
                </div>

                <div class="insight-item">
                    <div class="insight-icon" style="--insight-color: #FF9800;">
                        <span class="material-icons" style="font-size: 16px;">schedule</span>
                    </div>
                    <div class="insight-content">
                        <div class="insight-title">最佳記錄時段</div>
                        <div class="insight-text">你在晚上18-21時的記錄效果最好，建議在這個時段進行深度反思。</div>
                    </div>
                </div>

                <div class="insight-item">
                    <div class="insight-icon" style="--insight-color: #2196F3;">
                        <span class="material-icons" style="font-size: 16px;">psychology</span>
                    </div>
                    <div class="insight-content">
                        <div class="insight-title">情感平衡建議</div>
                        <div class="insight-text">你的「平靜」情感記錄較少，建議嘗試冥想或自然接觸來增加內心平靜。</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部導航 -->
        <div class="bottom-nav">
            <a href="main-app.html" class="nav-item">
                <span class="material-icons">home</span>
                <span class="nav-text">首頁</span>
            </a>
            <a href="diary.html" class="nav-item">
                <span class="material-icons">book</span>
                <span class="nav-text">日記</span>
            </a>
            <a href="vision-board.html" class="nav-item">
                <span class="material-icons">dashboard</span>
                <span class="nav-text">願景板</span>
            </a>
            <a href="#" class="nav-item active">
                <span class="material-icons">analytics</span>
                <span class="nav-text">統計</span>
            </a>
            <a href="profile.html" class="nav-item">
                <span class="material-icons">person</span>
                <span class="nav-text">個人</span>
            </a>
        </div>
    </div>

    <script>
        // 時間標籤切換
        document.querySelectorAll('.time-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.time-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                // 這裡可以添加數據更新邏輯
                console.log(`切換到：${this.textContent}`);
            });
        });

        // 熱力圖懸停效果
        document.querySelectorAll('.heatmap-cell').forEach(cell => {
            cell.addEventListener('mouseenter', function() {
                // 可以顯示詳細信息
                console.log('顯示詳細記錄信息');
            });
        });

        // 頁面加載動畫
        document.addEventListener('DOMContentLoaded', function() {
            const animateElements = document.querySelectorAll('.fade-in');
            animateElements.forEach((element, index) => {
                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // 添加脈衝效果到統計數字
            setTimeout(() => {
                document.querySelectorAll('.pulse').forEach(el => {
                    el.classList.add('pulse');
                });
            }, 1000);
        });
    </script>
</body>
</html>
