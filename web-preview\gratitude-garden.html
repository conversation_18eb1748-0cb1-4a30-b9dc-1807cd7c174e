<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>感恩花園 - 感恩與願景聯動模組</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            background: #F8F9FA;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #96CEB4, #FFEAA7);
            padding: 50px 20px 20px;
            border-radius: 0 0 25px 25px;
            text-align: center;
        }

        .header h1 {
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .header p {
            color: white;
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .garden-stats {
            display: flex;
            justify-content: space-around;
        }

        .garden-stat-item {
            text-align: center;
        }

        .garden-stat-item .material-icons {
            font-size: 24px;
            color: white;
            margin-bottom: 4px;
        }

        .garden-stat-text {
            font-size: 14px;
            color: white;
            font-weight: 500;
        }

        .content {
            padding: 20px;
        }

        .plant-card {
            background: white;
            border-radius: 16px;
            margin-bottom: 16px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            position: relative;
            transition: transform 0.3s ease;
        }

        .plant-card:hover {
            transform: translateY(-2px);
        }

        .plant-card.withering {
            background: linear-gradient(135deg, #F2F2F7, #E5E5EA);
        }

        .plant-gradient {
            padding: 20px;
            display: flex;
            align-items: center;
            position: relative;
        }

        .plant-icon-container {
            position: relative;
            margin-right: 16px;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .plant-icon {
            font-size: var(--icon-size);
            color: var(--plant-color);
            animation: var(--animation);
        }

        .sparkle {
            position: absolute;
            top: -5px;
            right: -5px;
            color: #FFD700;
            font-size: 16px;
            animation: sparkle 2s infinite;
        }

        @keyframes sparkle {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
        }

        @keyframes sway {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(-3deg); }
            75% { transform: rotate(3deg); }
        }

        .plant-info {
            flex: 1;
        }

        .plant-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }

        .plant-stage {
            font-size: 14px;
            color: #8E8E93;
            margin-bottom: 8px;
        }

        .plant-description {
            font-size: 12px;
            color: #666;
            margin-bottom: 12px;
            line-height: 16px;
        }

        .energy-container {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .energy-label {
            font-size: 12px;
            color: #8E8E93;
            width: 30px;
        }

        .energy-bar {
            flex: 1;
            height: 6px;
            background: #E5E5EA;
            border-radius: 3px;
            margin: 0 8px;
            overflow: hidden;
        }

        .energy-progress {
            height: 100%;
            border-radius: 3px;
            transition: width 0.8s ease;
        }

        .energy-value {
            font-size: 12px;
            color: #333;
            width: 35px;
            text-align: right;
        }

        .stats-container {
            display: flex;
            justify-content: space-between;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .stat-item .material-icons {
            font-size: 16px;
        }

        .stat-text {
            font-size: 12px;
            color: #666;
        }

        .water-button {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 40px;
            height: 40px;
            border-radius: 20px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            transition: transform 0.2s ease;
        }

        .water-button:hover {
            transform: scale(1.1);
        }

        .water-button:active {
            transform: scale(1.2);
        }

        .water-button .material-icons {
            color: white;
            font-size: 20px;
        }

        .warning-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            background: #FFF3CD;
            border-radius: 12px;
            padding: 4px;
        }

        .warning-badge .material-icons {
            color: #FF9500;
            font-size: 16px;
        }

        .tips-container {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .tips-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 16px;
        }

        .tip-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            gap: 12px;
        }

        .tip-item .material-icons {
            font-size: 16px;
            margin-top: 2px;
        }

        .tip-text {
            font-size: 14px;
            color: #666;
            flex: 1;
            line-height: 20px;
        }

        .watering-animation {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(78, 205, 196, 0.9);
            color: white;
            padding: 20px;
            border-radius: 16px;
            text-align: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .watering-animation.show {
            opacity: 1;
        }

        .watering-animation .material-icons {
            font-size: 48px;
            margin-bottom: 8px;
            animation: bounce 0.6s ease-in-out;
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 頂部漸變背景 -->
        <div class="header">
            <h1>感恩花園</h1>
            <p>每個植物代表一個願景，用感恩記錄為它們澆水吧！</p>
            <div class="garden-stats">
                <div class="garden-stat-item">
                    <div class="material-icons">local_florist</div>
                    <div class="garden-stat-text">5 株植物</div>
                </div>
                <div class="garden-stat-item">
                    <div class="material-icons">favorite</div>
                    <div class="garden-stat-text">119 次澆水</div>
                </div>
            </div>
        </div>

        <!-- 內容區域 -->
        <div class="content">
            <!-- 健康生活 - 盛開 -->
            <div class="plant-card fade-in" style="--plant-color: #FF6B6B; --icon-size: 50px; --animation: sway 4s ease-in-out infinite;">
                <div class="plant-gradient" style="background: linear-gradient(135deg, #FF6B6B20, #FF6B6B10);">
                    <div class="plant-icon-container">
                        <div class="material-icons plant-icon">local_florist</div>
                        <div class="material-icons sparkle">auto_awesome</div>
                    </div>
                    <div class="plant-info">
                        <div class="plant-title">健康生活</div>
                        <div class="plant-stage">盛開</div>
                        <div class="plant-description">這朵花代表你對健康生活的追求，每次感恩記錄都讓它更加茂盛。</div>
                        <div class="energy-container">
                            <div class="energy-label">能量</div>
                            <div class="energy-bar">
                                <div class="energy-progress" style="width: 85%; background-color: #FF6B6B;"></div>
                            </div>
                            <div class="energy-value">85%</div>
                        </div>
                        <div class="stats-container">
                            <div class="stat-item">
                                <span class="material-icons" style="color: #FF6B6B;">favorite</span>
                                <span class="stat-text">45 次感恩</span>
                            </div>
                            <div class="stat-item">
                                <span class="material-icons" style="color: #FF6B6B;">schedule</span>
                                <span class="stat-text">2 天前澆水</span>
                            </div>
                        </div>
                    </div>
                    <button class="water-button" style="background-color: #FF6B6B;" onclick="waterPlant('健康生活', '#FF6B6B')">
                        <span class="material-icons">opacity</span>
                    </button>
                </div>
            </div>

            <!-- 事業發展 - 成長中 -->
            <div class="plant-card fade-in" style="--plant-color: #4ECDC4; --icon-size: 40px; --animation: none;">
                <div class="plant-gradient" style="background: linear-gradient(135deg, #4ECDC420, #4ECDC410);">
                    <div class="plant-icon-container">
                        <div class="material-icons plant-icon">eco</div>
                    </div>
                    <div class="plant-info">
                        <div class="plant-title">事業發展</div>
                        <div class="plant-stage">成長中</div>
                        <div class="plant-description">這棵樹正在穩步成長，就像你的事業發展一樣。</div>
                        <div class="energy-container">
                            <div class="energy-label">能量</div>
                            <div class="energy-bar">
                                <div class="energy-progress" style="width: 60%; background-color: #4ECDC4;"></div>
                            </div>
                            <div class="energy-value">60%</div>
                        </div>
                        <div class="stats-container">
                            <div class="stat-item">
                                <span class="material-icons" style="color: #4ECDC4;">favorite</span>
                                <span class="stat-text">28 次感恩</span>
                            </div>
                            <div class="stat-item">
                                <span class="material-icons" style="color: #4ECDC4;">schedule</span>
                                <span class="stat-text">4 天前澆水</span>
                            </div>
                        </div>
                    </div>
                    <button class="water-button" style="background-color: #4ECDC4;" onclick="waterPlant('事業發展', '#4ECDC4')">
                        <span class="material-icons">opacity</span>
                    </button>
                </div>
            </div>

            <!-- 人際關係 - 盛開 -->
            <div class="plant-card fade-in" style="--plant-color: #45B7D1; --icon-size: 50px; --animation: sway 3s ease-in-out infinite;">
                <div class="plant-gradient" style="background: linear-gradient(135deg, #45B7D120, #45B7D110);">
                    <div class="plant-icon-container">
                        <div class="material-icons plant-icon">nature</div>
                        <div class="material-icons sparkle">auto_awesome</div>
                    </div>
                    <div class="plant-info">
                        <div class="plant-title">人際關係</div>
                        <div class="plant-stage">盛開</div>
                        <div class="plant-description">愛的花朵正在盛開，你的人際關係越來越美好。</div>
                        <div class="energy-container">
                            <div class="energy-label">能量</div>
                            <div class="energy-bar">
                                <div class="energy-progress" style="width: 78%; background-color: #45B7D1;"></div>
                            </div>
                            <div class="energy-value">78%</div>
                        </div>
                        <div class="stats-container">
                            <div class="stat-item">
                                <span class="material-icons" style="color: #45B7D1;">favorite</span>
                                <span class="stat-text">35 次感恩</span>
                            </div>
                            <div class="stat-item">
                                <span class="material-icons" style="color: #45B7D1;">schedule</span>
                                <span class="stat-text">3 天前澆水</span>
                            </div>
                        </div>
                    </div>
                    <button class="water-button" style="background-color: #45B7D1;" onclick="waterPlant('人際關係', '#45B7D1')">
                        <span class="material-icons">opacity</span>
                    </button>
                </div>
            </div>

            <!-- 學習成長 - 嫩芽 -->
            <div class="plant-card fade-in" style="--plant-color: #96CEB4; --icon-size: 30px; --animation: none;">
                <div class="plant-gradient" style="background: linear-gradient(135deg, #96CEB420, #96CEB410);">
                    <div class="plant-icon-container">
                        <div class="material-icons plant-icon">grass</div>
                    </div>
                    <div class="plant-info">
                        <div class="plant-title">學習成長</div>
                        <div class="plant-stage">嫩芽</div>
                        <div class="plant-description">新的嫩芽需要更多關注，記得為學習成長澆水。</div>
                        <div class="energy-container">
                            <div class="energy-label">能量</div>
                            <div class="energy-bar">
                                <div class="energy-progress" style="width: 25%; background-color: #96CEB4;"></div>
                            </div>
                            <div class="energy-value">25%</div>
                        </div>
                        <div class="stats-container">
                            <div class="stat-item">
                                <span class="material-icons" style="color: #96CEB4;">favorite</span>
                                <span class="stat-text">8 次感恩</span>
                            </div>
                            <div class="stat-item">
                                <span class="material-icons" style="color: #96CEB4;">schedule</span>
                                <span class="stat-text">12 天前澆水</span>
                            </div>
                        </div>
                    </div>
                    <button class="water-button" style="background-color: #96CEB4;" onclick="waterPlant('學習成長', '#96CEB4')">
                        <span class="material-icons">opacity</span>
                    </button>
                    <div class="warning-badge">
                        <span class="material-icons">warning</span>
                    </div>
                </div>
            </div>

            <!-- 財務自由 - 枯萎 -->
            <div class="plant-card fade-in withering" style="--plant-color: #8E8E93; --icon-size: 35px; --animation: none;">
                <div class="plant-gradient">
                    <div class="plant-icon-container">
                        <div class="material-icons plant-icon">local_florist</div>
                    </div>
                    <div class="plant-info">
                        <div class="plant-title">財務自由</div>
                        <div class="plant-stage">枯萎</div>
                        <div class="plant-description">這朵花需要你的關注，長期沒有感恩記錄讓它有些枯萎。</div>
                        <div class="energy-container">
                            <div class="energy-label">能量</div>
                            <div class="energy-bar">
                                <div class="energy-progress" style="width: 15%; background-color: #8E8E93;"></div>
                            </div>
                            <div class="energy-value">15%</div>
                        </div>
                        <div class="stats-container">
                            <div class="stat-item">
                                <span class="material-icons" style="color: #8E8E93;">favorite</span>
                                <span class="stat-text">3 次感恩</span>
                            </div>
                            <div class="stat-item">
                                <span class="material-icons" style="color: #8E8E93;">schedule</span>
                                <span class="stat-text">17 天前澆水</span>
                            </div>
                        </div>
                    </div>
                    <button class="water-button" style="background-color: #8E8E93;" onclick="waterPlant('財務自由', '#8E8E93')">
                        <span class="material-icons">opacity</span>
                    </button>
                    <div class="warning-badge">
                        <span class="material-icons">warning</span>
                    </div>
                </div>
            </div>

            <!-- 澆水小貼士 -->
            <div class="tips-container">
                <div class="tips-title">澆水小貼士</div>
                <div class="tip-item">
                    <span class="material-icons" style="color: #FFD700;">lightbulb</span>
                    <div class="tip-text">定期為願景記錄感恩日記，讓植物茁壯成長</div>
                </div>
                <div class="tip-item">
                    <span class="material-icons" style="color: #FF9500;">warning</span>
                    <div class="tip-text">超過7天未澆水的植物會開始枯萎</div>
                </div>
                <div class="tip-item">
                    <span class="material-icons" style="color: #FF6B6B;">auto_awesome</span>
                    <div class="tip-text">盛開的花朵表示你在這個願景上表現優秀</div>
                </div>
            </div>
        </div>

        <!-- 澆水動畫 -->
        <div class="watering-animation" id="wateringAnimation">
            <div class="material-icons">opacity</div>
            <div id="wateringText">為健康生活澆水成功！</div>
        </div>
    </div>

    <script>
        // 澆水功能
        function waterPlant(plantName, color) {
            const animation = document.getElementById('wateringAnimation');
            const text = document.getElementById('wateringText');
            
            text.textContent = `為${plantName}澆水成功！`;
            animation.style.background = `rgba(${hexToRgb(color)}, 0.9)`;
            animation.classList.add('show');
            
            // 2秒後隱藏動畫
            setTimeout(() => {
                animation.classList.remove('show');
            }, 2000);
            
            console.log(`為 ${plantName} 澆水`);
        }

        // 十六進制顏色轉RGB
        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? 
                `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` : 
                '78, 205, 196';
        }

        // 頁面加載動畫
        document.addEventListener('DOMContentLoaded', function() {
            const animateElements = document.querySelectorAll('.fade-in');
            animateElements.forEach((element, index) => {
                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
