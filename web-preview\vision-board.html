<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>願景板 - 感恩願景App</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            background: #F8F9FA;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            position: relative;
        }

        .header {
            background: linear-gradient(135deg, #45B7D1, #96CEB4);
            padding: 50px 20px 20px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .header p {
            color: white;
            font-size: 16px;
            opacity: 0.9;
        }

        .search-filter {
            background: white;
            margin: -20px 20px 0;
            border-radius: 16px;
            padding: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            z-index: 2;
        }

        .search-bar {
            display: flex;
            align-items: center;
            background: #F8F9FA;
            border-radius: 12px;
            padding: 12px 16px;
            margin-bottom: 12px;
        }

        .search-bar .material-icons {
            color: #8E8E93;
            margin-right: 8px;
        }

        .search-input {
            flex: 1;
            border: none;
            background: none;
            font-size: 16px;
            outline: none;
        }

        .filter-tags {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            padding-bottom: 4px;
        }

        .filter-tag {
            padding: 6px 12px;
            border-radius: 16px;
            background: #F2F2F7;
            color: #8E8E93;
            font-size: 12px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-tag.active {
            background: #45B7D1;
            color: white;
        }

        .content {
            padding: 30px 20px 100px;
            background: #F8F9FA;
        }

        .vision-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .vision-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .vision-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .vision-thumbnail {
            height: 120px;
            background: var(--vision-bg);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .vision-content {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            padding: 16px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            color: white;
            text-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }

        .vision-title-overlay {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
        }

        .vision-elements {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .vision-element {
            width: 24px;
            height: 24px;
            border-radius: 12px;
            background: rgba(255,255,255,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .progress-ring {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: conic-gradient(white 0deg var(--progress-deg), rgba(255,255,255,0.3) var(--progress-deg) 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: white;
        }

        .vision-info {
            padding: 16px;
        }

        .vision-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .vision-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #8E8E93;
        }

        .vision-date {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .vision-category {
            background: var(--category-color);
            color: white;
            padding: 2px 8px;
            border-radius: 8px;
            font-size: 10px;
        }

        .add-button {
            position: fixed;
            bottom: 120px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: #45B7D1;
            border: none;
            border-radius: 28px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 16px rgba(69, 183, 209, 0.4);
            transition: all 0.3s ease;
            z-index: 10;
        }

        .add-button:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(69, 183, 209, 0.6);
        }

        .add-button .material-icons {
            font-size: 28px;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #E5E5EA;
            padding: 8px 0;
            display: flex;
            justify-content: space-around;
            z-index: 100;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #8E8E93;
        }

        .nav-item.active {
            color: #FF6B6B;
        }

        .nav-item .material-icons {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .nav-text {
            font-size: 12px;
            font-weight: 500;
        }

        .create-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: flex-end;
            z-index: 1000;
        }

        .create-modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 20px 20px 0 0;
            width: 100%;
            max-width: 414px;
            max-height: 80vh;
            overflow-y: auto;
            animation: slideUp 0.3s ease;
        }

        @keyframes slideUp {
            from { transform: translateY(100%); }
            to { transform: translateY(0); }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #E5E5EA;
        }

        .modal-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
        }

        .close-button {
            padding: 4px;
            cursor: pointer;
            color: #8E8E93;
        }

        .modal-body {
            padding: 20px;
        }

        .template-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }

        .template-card {
            aspect-ratio: 3/2;
            border-radius: 12px;
            background: var(--template-bg);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
        }

        .template-card:hover {
            transform: scale(1.05);
        }

        .template-card.selected {
            border-color: #45B7D1;
        }

        .template-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
            color: white;
            padding: 8px;
            text-align: center;
            font-size: 12px;
            font-weight: 500;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: block;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #E5E5EA;
            border-radius: 12px;
            font-size: 16px;
            font-family: inherit;
        }

        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }

        .create-button {
            width: 100%;
            background: #45B7D1;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .create-button:hover {
            background: #3A9BC1;
        }

        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 頂部標題 -->
        <div class="header">
            <h1>願景板</h1>
            <p>創建和管理你的夢想</p>
        </div>

        <!-- 搜索和篩選 -->
        <div class="search-filter fade-in">
            <div class="search-bar">
                <span class="material-icons">search</span>
                <input type="text" class="search-input" placeholder="搜索願景板...">
            </div>
            <div class="filter-tags">
                <div class="filter-tag active">全部</div>
                <div class="filter-tag">事業</div>
                <div class="filter-tag">健康</div>
                <div class="filter-tag">關係</div>
                <div class="filter-tag">學習</div>
                <div class="filter-tag">財務</div>
            </div>
        </div>

        <!-- 主要內容 -->
        <div class="content">
            <div class="vision-grid">
                <!-- 願景板1 - 健康生活 -->
                <div class="vision-card fade-in" onclick="viewVision(1)">
                    <div class="vision-thumbnail" style="--vision-bg: linear-gradient(135deg, #FF6B6B, #FF8E8E);">
                        <div class="vision-content">
                            <div class="vision-title-overlay">健康生活</div>
                            <div class="vision-elements">
                                <div class="vision-element">🏃</div>
                                <div class="vision-element">🥗</div>
                                <div class="vision-element">💪</div>
                            </div>
                        </div>
                        <div class="progress-ring" style="--progress-deg: 270deg;">75%</div>
                    </div>
                    <div class="vision-info">
                        <div class="vision-title">健康生活</div>
                        <div class="vision-meta">
                            <div class="vision-date">
                                <span class="material-icons" style="font-size: 12px;">schedule</span>
                                1月15日
                            </div>
                            <div class="vision-category" style="--category-color: #FF6B6B;">健康</div>
                        </div>
                    </div>
                </div>

                <!-- 願景板2 - 事業發展 -->
                <div class="vision-card fade-in" onclick="viewVision(2)">
                    <div class="vision-thumbnail" style="--vision-bg: linear-gradient(135deg, #4ECDC4, #6DD4CB);">
                        <div class="vision-content">
                            <div class="vision-title-overlay">事業發展</div>
                            <div class="vision-elements">
                                <div class="vision-element">💼</div>
                                <div class="vision-element">📈</div>
                                <div class="vision-element">🎯</div>
                            </div>
                        </div>
                        <div class="progress-ring" style="--progress-deg: 162deg;">45%</div>
                    </div>
                    <div class="vision-info">
                        <div class="vision-title">事業發展</div>
                        <div class="vision-meta">
                            <div class="vision-date">
                                <span class="material-icons" style="font-size: 12px;">schedule</span>
                                1月10日
                            </div>
                            <div class="vision-category" style="--category-color: #4ECDC4;">事業</div>
                        </div>
                    </div>
                </div>

                <!-- 願景板3 - 人際關係 -->
                <div class="vision-card fade-in" onclick="viewVision(3)">
                    <div class="vision-thumbnail" style="--vision-bg: linear-gradient(135deg, #45B7D1, #6BC5D8);">
                        <div class="vision-content">
                            <div class="vision-title-overlay">人際關係</div>
                            <div class="vision-elements">
                                <div class="vision-element">👨‍👩‍👧‍👦</div>
                                <div class="vision-element">❤️</div>
                                <div class="vision-element">🤝</div>
                            </div>
                        </div>
                        <div class="progress-ring" style="--progress-deg: 223deg;">62%</div>
                    </div>
                    <div class="vision-info">
                        <div class="vision-title">人際關係</div>
                        <div class="vision-meta">
                            <div class="vision-date">
                                <span class="material-icons" style="font-size: 12px;">schedule</span>
                                1月8日
                            </div>
                            <div class="vision-category" style="--category-color: #45B7D1;">關係</div>
                        </div>
                    </div>
                </div>

                <!-- 願景板4 - 學習成長 -->
                <div class="vision-card fade-in" onclick="viewVision(4)">
                    <div class="vision-thumbnail" style="--vision-bg: linear-gradient(135deg, #96CEB4, #A8D5C1);">
                        <div class="vision-content">
                            <div class="vision-title-overlay">學習成長</div>
                            <div class="vision-elements">
                                <div class="vision-element">📚</div>
                                <div class="vision-element">🧠</div>
                                <div class="vision-element">🌱</div>
                            </div>
                        </div>
                        <div class="progress-ring" style="--progress-deg: 90deg;">25%</div>
                    </div>
                    <div class="vision-info">
                        <div class="vision-title">學習成長</div>
                        <div class="vision-meta">
                            <div class="vision-date">
                                <span class="material-icons" style="font-size: 12px;">schedule</span>
                                1月5日
                            </div>
                            <div class="vision-category" style="--category-color: #96CEB4;">學習</div>
                        </div>
                    </div>
                </div>

                <!-- 願景板5 - 財務自由 -->
                <div class="vision-card fade-in" onclick="viewVision(5)">
                    <div class="vision-thumbnail" style="--vision-bg: linear-gradient(135deg, #FFEAA7, #FFEFB8);">
                        <div class="vision-content">
                            <div class="vision-title-overlay">財務自由</div>
                            <div class="vision-elements">
                                <div class="vision-element">💰</div>
                                <div class="vision-element">🏠</div>
                                <div class="vision-element">✈️</div>
                            </div>
                        </div>
                        <div class="progress-ring" style="--progress-deg: 54deg;">15%</div>
                    </div>
                    <div class="vision-info">
                        <div class="vision-title">財務自由</div>
                        <div class="vision-meta">
                            <div class="vision-date">
                                <span class="material-icons" style="font-size: 12px;">schedule</span>
                                1月3日
                            </div>
                            <div class="vision-category" style="--category-color: #FFEAA7;">財務</div>
                        </div>
                    </div>
                </div>

                <!-- 願景板6 - 旅行夢想 -->
                <div class="vision-card fade-in" onclick="viewVision(6)">
                    <div class="vision-thumbnail" style="--vision-bg: linear-gradient(135deg, #DDA0DD, #E6B3E6);">
                        <div class="vision-content">
                            <div class="vision-title-overlay">旅行夢想</div>
                            <div class="vision-elements">
                                <div class="vision-element">🗺️</div>
                                <div class="vision-element">📸</div>
                                <div class="vision-element">🎒</div>
                            </div>
                        </div>
                        <div class="progress-ring" style="--progress-deg: 108deg;">30%</div>
                    </div>
                    <div class="vision-info">
                        <div class="vision-title">旅行夢想</div>
                        <div class="vision-meta">
                            <div class="vision-date">
                                <span class="material-icons" style="font-size: 12px;">schedule</span>
                                12月28日
                            </div>
                            <div class="vision-category" style="--category-color: #DDA0DD;">生活</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 新建按鈕 -->
        <button class="add-button" onclick="showCreateModal()">
            <span class="material-icons">add</span>
        </button>

        <!-- 底部導航 -->
        <div class="bottom-nav">
            <a href="main-app.html" class="nav-item">
                <span class="material-icons">home</span>
                <span class="nav-text">首頁</span>
            </a>
            <a href="diary.html" class="nav-item">
                <span class="material-icons">book</span>
                <span class="nav-text">日記</span>
            </a>
            <a href="#" class="nav-item active">
                <span class="material-icons">dashboard</span>
                <span class="nav-text">願景板</span>
            </a>
            <a href="index.html" class="nav-item">
                <span class="material-icons">analytics</span>
                <span class="nav-text">統計</span>
            </a>
            <a href="profile.html" class="nav-item">
                <span class="material-icons">person</span>
                <span class="nav-text">個人</span>
            </a>
        </div>

        <!-- 創建願景板模態框 -->
        <div class="create-modal" id="createModal">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title">創建新願景板</div>
                    <div class="close-button" onclick="hideCreateModal()">
                        <span class="material-icons">close</span>
                    </div>
                </div>
                <div class="modal-body">
                    <!-- 模板選擇 -->
                    <div class="form-group">
                        <label class="form-label">選擇模板</label>
                        <div class="template-grid">
                            <div class="template-card" style="--template-bg: linear-gradient(135deg, #FF6B6B, #FF8E8E);" onclick="selectTemplate(this)">
                                <div class="template-overlay">經典布局</div>
                            </div>
                            <div class="template-card" style="--template-bg: linear-gradient(135deg, #4ECDC4, #6DD4CB);" onclick="selectTemplate(this)">
                                <div class="template-overlay">網格布局</div>
                            </div>
                            <div class="template-card" style="--template-bg: linear-gradient(135deg, #45B7D1, #6BC5D8);" onclick="selectTemplate(this)">
                                <div class="template-overlay">時間軸</div>
                            </div>
                            <div class="template-card" style="--template-bg: linear-gradient(135deg, #96CEB4, #A8D5C1);" onclick="selectTemplate(this)">
                                <div class="template-overlay">自由布局</div>
                            </div>
                        </div>
                    </div>

                    <!-- 基本信息 -->
                    <div class="form-group">
                        <label class="form-label">願景標題</label>
                        <input type="text" class="form-input" placeholder="輸入你的願景標題...">
                    </div>

                    <div class="form-group">
                        <label class="form-label">願景描述</label>
                        <textarea class="form-input form-textarea" placeholder="描述你的願景和目標..."></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label">目標日期</label>
                        <input type="date" class="form-input">
                    </div>

                    <button class="create-button" onclick="createVision()">創建願景板</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 顯示創建模態框
        function showCreateModal() {
            document.getElementById('createModal').classList.add('show');
        }

        // 隱藏創建模態框
        function hideCreateModal() {
            document.getElementById('createModal').classList.remove('show');
        }

        // 選擇模板
        function selectTemplate(element) {
            document.querySelectorAll('.template-card').forEach(card => {
                card.classList.remove('selected');
            });
            element.classList.add('selected');
        }

        // 查看願景板
        function viewVision(id) {
            // 跳轉到編輯頁面
            window.location.href = 'vision-editor.html';
        }

        // 創建願景板
        function createVision() {
            const title = document.querySelector('input[placeholder="輸入你的願景標題..."]').value;
            const description = document.querySelector('textarea').value;
            const targetDate = document.querySelector('input[type="date"]').value;
            const selectedTemplate = document.querySelector('.template-card.selected');
            
            if (!title.trim()) {
                alert('請輸入願景標題');
                return;
            }
            
            if (!selectedTemplate) {
                alert('請選擇一個模板');
                return;
            }
            
            alert(`願景板創建成功！\n標題：${title}\n描述：${description}\n目標日期：${targetDate}`);
            hideCreateModal();
        }

        // 篩選標籤
        document.querySelectorAll('.filter-tag').forEach(tag => {
            tag.addEventListener('click', function() {
                document.querySelectorAll('.filter-tag').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 點擊模態框外部關閉
        document.getElementById('createModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideCreateModal();
            }
        });

        // 頁面加載動畫
        document.addEventListener('DOMContentLoaded', function() {
            const animateElements = document.querySelectorAll('.fade-in');
            animateElements.forEach((element, index) => {
                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
