<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成長回顧 - 感恩與願景聯動模組</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            background: #F8F9FA;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            padding: 50px 20px 20px;
            border-radius: 0 0 25px 25px;
            text-align: center;
        }

        .header h1 {
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .header p {
            color: white;
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .year-selector {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

        .year-button {
            padding: 8px 20px;
            border-radius: 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            margin: 0 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .year-button.active {
            background: white;
            color: #667eea;
        }

        .play-button {
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 25px;
            padding: 12px 20px;
            color: white;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .play-button:hover {
            background: rgba(255,255,255,0.3);
        }

        .play-button .material-icons {
            font-size: 24px;
            margin-right: 8px;
        }

        .content {
            padding: 20px;
        }

        .stats-overview {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
        }

        .stat-card {
            flex: 1;
            background: white;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #8E8E93;
        }

        .timeline-container {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .timeline-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .timeline-item {
            display: flex;
            margin-bottom: 20px;
            opacity: 0;
            transform: translateX(-20px);
            transition: all 0.8s ease;
        }

        .timeline-item.animate {
            opacity: 1;
            transform: translateX(0);
        }

        .timeline-date {
            width: 60px;
            text-align: center;
            padding-top: 8px;
        }

        .timeline-date-text {
            font-size: 12px;
            color: #8E8E93;
            font-weight: 500;
        }

        .timeline-connector {
            width: 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .timeline-dot {
            width: 12px;
            height: 12px;
            border-radius: 6px;
            margin-top: 8px;
            position: relative;
        }

        .timeline-dot.pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 107, 107, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0); }
        }

        .timeline-line {
            width: 2px;
            flex: 1;
            background: #E5E5EA;
            margin-top: 4px;
        }

        .timeline-content {
            flex: 1;
            margin-left: 12px;
        }

        .milestone-card {
            border-radius: 12px;
            padding: 16px;
            background: linear-gradient(135deg, var(--milestone-color-light), var(--milestone-color-lighter));
        }

        .milestone-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .milestone-header .material-icons {
            font-size: 20px;
            color: var(--milestone-color);
            margin-right: 8px;
        }

        .milestone-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            flex: 1;
        }

        .milestone-emotion {
            font-size: 12px;
            color: #666;
        }

        .emotion-journey-container {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .emotion-journey-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            text-align: center;
        }

        .emotion-journey-subtitle {
            font-size: 14px;
            color: #8E8E93;
            text-align: center;
            margin-bottom: 20px;
        }

        .emotion-chart {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            height: 120px;
        }

        .emotion-bar {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 2px;
        }

        .emotion-bar-fill {
            width: 80%;
            border-radius: 4px;
            margin-bottom: 8px;
            transition: height 1s ease;
            background: linear-gradient(to top, var(--bar-color), var(--bar-color-light));
        }

        .emotion-bar-label {
            font-size: 10px;
            color: #8E8E93;
        }

        .share-section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .share-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            text-align: center;
        }

        .share-description {
            font-size: 14px;
            color: #8E8E93;
            text-align: center;
            margin-bottom: 20px;
            line-height: 20px;
        }

        .share-buttons {
            display: flex;
            gap: 8px;
        }

        .share-button {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #FF6B6B;
            border: none;
            border-radius: 12px;
            padding: 12px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .share-button:hover {
            background: #FF5252;
        }

        .share-button.secondary {
            background: white;
            border: 1px solid #FF6B6B;
            color: #FF6B6B;
        }

        .share-button.secondary:hover {
            background: #FF6B6B;
            color: white;
        }

        .share-button .material-icons {
            font-size: 20px;
            margin-right: 8px;
        }

        .playing-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(102, 126, 234, 0.9);
            color: white;
            padding: 20px;
            border-radius: 16px;
            text-align: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .playing-indicator.show {
            opacity: 1;
        }

        .playing-indicator .material-icons {
            font-size: 48px;
            margin-bottom: 8px;
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 頂部漸變背景 -->
        <div class="header">
            <h1>成長動畫回顧</h1>
            <p>回顧你的感恩成長軌跡</p>
            
            <div class="year-selector">
                <button class="year-button active">2024</button>
            </div>

            <button class="play-button" onclick="playYearlyReview()">
                <span class="material-icons" id="playIcon">play_arrow</span>
                <span id="playText">播放年度回顧</span>
            </button>
        </div>

        <!-- 內容區域 -->
        <div class="content">
            <!-- 統計概覽 -->
            <div class="stats-overview fade-in">
                <div class="stat-card">
                    <div class="stat-number">156</div>
                    <div class="stat-label">感恩記錄</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">5</div>
                    <div class="stat-label">願景數量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">5</div>
                    <div class="stat-label">重要里程碑</div>
                </div>
            </div>

            <!-- 時間軸 -->
            <div class="timeline-container fade-in">
                <div class="timeline-title">重要時刻回顧</div>
                
                <!-- 里程碑1 -->
                <div class="timeline-item" data-delay="0">
                    <div class="timeline-date">
                        <div class="timeline-date-text">1月15日</div>
                    </div>
                    <div class="timeline-connector">
                        <div class="timeline-dot" style="background-color: #FF6B6B;"></div>
                        <div class="timeline-line"></div>
                    </div>
                    <div class="timeline-content">
                        <div class="milestone-card" style="--milestone-color: #FF6B6B; --milestone-color-light: #FF6B6B20; --milestone-color-lighter: #FF6B6B10;">
                            <div class="milestone-header">
                                <span class="material-icons">star</span>
                                <div class="milestone-title">首次記錄感恩日記</div>
                            </div>
                            <div class="milestone-emotion">主要情感：喜悅</div>
                        </div>
                    </div>
                </div>

                <!-- 里程碑2 -->
                <div class="timeline-item" data-delay="1">
                    <div class="timeline-date">
                        <div class="timeline-date-text">2月1日</div>
                    </div>
                    <div class="timeline-connector">
                        <div class="timeline-dot" style="background-color: #4ECDC4;"></div>
                        <div class="timeline-line"></div>
                    </div>
                    <div class="timeline-content">
                        <div class="milestone-card" style="--milestone-color: #4ECDC4; --milestone-color-light: #4ECDC420; --milestone-color-lighter: #4ECDC410;">
                            <div class="milestone-header">
                                <span class="material-icons">local_fire_department</span>
                                <div class="milestone-title">連續記錄7天</div>
                            </div>
                            <div class="milestone-emotion">主要情感：滿足</div>
                        </div>
                    </div>
                </div>

                <!-- 里程碑3 -->
                <div class="timeline-item" data-delay="2">
                    <div class="timeline-date">
                        <div class="timeline-date-text">3月10日</div>
                    </div>
                    <div class="timeline-connector">
                        <div class="timeline-dot" style="background-color: #45B7D1;"></div>
                        <div class="timeline-line"></div>
                    </div>
                    <div class="timeline-content">
                        <div class="milestone-card" style="--milestone-color: #45B7D1; --milestone-color-light: #45B7D120; --milestone-color-lighter: #45B7D110;">
                            <div class="milestone-header">
                                <span class="material-icons">flag</span>
                                <div class="milestone-title">完成健康生活願景25%</div>
                            </div>
                            <div class="milestone-emotion">主要情感：希望</div>
                        </div>
                    </div>
                </div>

                <!-- 里程碑4 -->
                <div class="timeline-item" data-delay="3">
                    <div class="timeline-date">
                        <div class="timeline-date-text">4月20日</div>
                    </div>
                    <div class="timeline-connector">
                        <div class="timeline-dot" style="background-color: #96CEB4;"></div>
                        <div class="timeline-line"></div>
                    </div>
                    <div class="timeline-content">
                        <div class="milestone-card" style="--milestone-color: #96CEB4; --milestone-color-light: #96CEB420; --milestone-color-lighter: #96CEB410;">
                            <div class="milestone-header">
                                <span class="material-icons">local_fire_department</span>
                                <div class="milestone-title">連續記錄30天</div>
                            </div>
                            <div class="milestone-emotion">主要情感：愛</div>
                        </div>
                    </div>
                </div>

                <!-- 里程碑5 -->
                <div class="timeline-item" data-delay="4">
                    <div class="timeline-date">
                        <div class="timeline-date-text">6月15日</div>
                    </div>
                    <div class="timeline-connector">
                        <div class="timeline-dot" style="background-color: #FFEAA7;"></div>
                    </div>
                    <div class="timeline-content">
                        <div class="milestone-card" style="--milestone-color: #FFEAA7; --milestone-color-light: #FFEAA720; --milestone-color-lighter: #FFEAA710;">
                            <div class="milestone-header">
                                <span class="material-icons">emoji_events</span>
                                <div class="milestone-title">收集30種積極情感</div>
                            </div>
                            <div class="milestone-emotion">主要情感：感恩</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 情感變化圖 -->
            <div class="emotion-journey-container fade-in">
                <div class="emotion-journey-title">情感變化軌跡</div>
                <div class="emotion-journey-subtitle">
                    從冷色調到暖色調的轉變代表積極情緒的增長
                </div>
                
                <div class="emotion-chart">
                    <div class="emotion-bar">
                        <div class="emotion-bar-fill" data-height="20" style="--bar-color: #8E8E93; --bar-color-light: #A5A5A5;"></div>
                        <div class="emotion-bar-label">1月</div>
                    </div>
                    <div class="emotion-bar">
                        <div class="emotion-bar-fill" data-height="30" style="--bar-color: #45B7D1; --bar-color-light: #6BC5D8;"></div>
                        <div class="emotion-bar-label">2月</div>
                    </div>
                    <div class="emotion-bar">
                        <div class="emotion-bar-fill" data-height="40" style="--bar-color: #4ECDC4; --bar-color-light: #6DD4CB;"></div>
                        <div class="emotion-bar-label">3月</div>
                    </div>
                    <div class="emotion-bar">
                        <div class="emotion-bar-fill" data-height="60" style="--bar-color: #96CEB4; --bar-color-light: #A8D5C1;"></div>
                        <div class="emotion-bar-label">4月</div>
                    </div>
                    <div class="emotion-bar">
                        <div class="emotion-bar-fill" data-height="70" style="--bar-color: #FFEAA7; --bar-color-light: #FFEFB8;"></div>
                        <div class="emotion-bar-label">5月</div>
                    </div>
                    <div class="emotion-bar">
                        <div class="emotion-bar-fill" data-height="80" style="--bar-color: #FF6B6B; --bar-color-light: #FF8E8E;"></div>
                        <div class="emotion-bar-label">6月</div>
                    </div>
                </div>
            </div>

            <!-- 分享功能 -->
            <div class="share-section fade-in">
                <div class="share-title">分享你的成長故事</div>
                <div class="share-description">
                    生成精美的年度回顧視頻，與親友分享你的感恩成長軌跡
                </div>
                
                <div class="share-buttons">
                    <button class="share-button" onclick="generateVideo()">
                        <span class="material-icons">video_library</span>
                        生成視頻
                    </button>
                    
                    <button class="share-button secondary" onclick="shareImage()">
                        <span class="material-icons">share</span>
                        分享圖片
                    </button>
                </div>
            </div>
        </div>

        <!-- 播放指示器 -->
        <div class="playing-indicator" id="playingIndicator">
            <div class="material-icons">autorenew</div>
            <div>正在生成年度回顧...</div>
        </div>
    </div>

    <script>
        let isPlaying = false;

        // 播放年度回顧動畫
        function playYearlyReview() {
            if (isPlaying) return;
            
            isPlaying = true;
            const playButton = document.querySelector('.play-button');
            const playIcon = document.getElementById('playIcon');
            const playText = document.getElementById('playText');
            const indicator = document.getElementById('playingIndicator');
            
            // 更新按鈕狀態
            playIcon.textContent = 'pause';
            playText.textContent = '播放中...';
            playButton.style.pointerEvents = 'none';
            
            // 顯示播放指示器
            indicator.classList.add('show');
            
            // 重置時間軸動畫
            const timelineItems = document.querySelectorAll('.timeline-item');
            timelineItems.forEach(item => {
                item.classList.remove('animate');
            });
            
            // 重置情感圖表
            const emotionBars = document.querySelectorAll('.emotion-bar-fill');
            emotionBars.forEach(bar => {
                bar.style.height = '0%';
            });
            
            // 開始動畫序列
            setTimeout(() => {
                indicator.classList.remove('show');
                
                // 時間軸動畫
                timelineItems.forEach((item, index) => {
                    setTimeout(() => {
                        item.classList.add('animate');
                        
                        // 添加脈衝效果到當前項目
                        const dot = item.querySelector('.timeline-dot');
                        dot.classList.add('pulse');
                        
                        // 移除之前項目的脈衝效果
                        if (index > 0) {
                            const prevDot = timelineItems[index - 1].querySelector('.timeline-dot');
                            prevDot.classList.remove('pulse');
                        }
                        
                        // 最後一個項目完成後開始情感圖表動畫
                        if (index === timelineItems.length - 1) {
                            setTimeout(() => {
                                dot.classList.remove('pulse');
                                animateEmotionChart();
                            }, 800);
                        }
                    }, index * 600);
                });
            }, 1000);
        }

        // 情感圖表動畫
        function animateEmotionChart() {
            const emotionBars = document.querySelectorAll('.emotion-bar-fill');
            
            emotionBars.forEach((bar, index) => {
                setTimeout(() => {
                    const height = bar.getAttribute('data-height');
                    bar.style.height = height + '%';
                    
                    // 最後一個完成後重置播放按鈕
                    if (index === emotionBars.length - 1) {
                        setTimeout(() => {
                            resetPlayButton();
                        }, 1000);
                    }
                }, index * 200);
            });
        }

        // 重置播放按鈕
        function resetPlayButton() {
            isPlaying = false;
            const playButton = document.querySelector('.play-button');
            const playIcon = document.getElementById('playIcon');
            const playText = document.getElementById('playText');
            
            playIcon.textContent = 'play_arrow';
            playText.textContent = '播放年度回顧';
            playButton.style.pointerEvents = 'auto';
        }

        // 生成視頻
        function generateVideo() {
            alert('正在生成年度回顧視頻...');
        }

        // 分享圖片
        function shareImage() {
            alert('正在生成分享圖片...');
        }

        // 頁面加載動畫
        document.addEventListener('DOMContentLoaded', function() {
            const animateElements = document.querySelectorAll('.fade-in');
            animateElements.forEach((element, index) => {
                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
