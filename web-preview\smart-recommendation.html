<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能推薦 - 感恩與願景聯動模組</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            background: #F8F9FA;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            padding: 50px 20px 20px;
            border-radius: 0 0 25px 25px;
            text-align: center;
        }

        .header h1 {
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .header p {
            color: white;
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .stats-grid {
            display: flex;
            justify-content: space-between;
        }

        .stat-card {
            flex: 1;
            text-align: center;
            background: rgba(255,255,255,0.2);
            border-radius: 12px;
            padding: 16px;
            margin: 0 4px;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: white;
            opacity: 0.9;
        }

        .content {
            padding: 20px;
        }

        .recommendation-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .recommendation-card:hover {
            transform: translateY(-2px);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .diary-info {
            flex: 1;
        }

        .diary-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }

        .timestamp {
            font-size: 12px;
            color: #8E8E93;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background: #FFF3CD;
            color: #856404;
        }

        .status-accepted {
            background: #D4EDDA;
            color: #155724;
        }

        .status-rejected {
            background: #F8D7DA;
            color: #721c24;
        }

        .diary-content {
            font-size: 14px;
            color: #666;
            line-height: 20px;
            margin-bottom: 16px;
        }

        .recommendation-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }

        .vision-recommendation {
            margin-bottom: 12px;
        }

        .vision-card {
            background: linear-gradient(135deg, #F8F9FA, #FFFFFF);
            border-radius: 12px;
            padding: 16px;
            border: 1px solid #E5E5EA;
        }

        .vision-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .vision-info {
            flex: 1;
        }

        .vision-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .match-reason {
            font-size: 12px;
            color: #8E8E93;
        }

        .match-score {
            text-align: center;
        }

        .match-percentage {
            font-size: 18px;
            font-weight: bold;
            color: #FF6B6B;
        }

        .match-label {
            font-size: 10px;
            color: #8E8E93;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .action-button {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .accept-button {
            background: #4CAF50;
            color: white;
        }

        .accept-button:hover {
            background: #45a049;
        }

        .reject-button {
            background: #F2F2F7;
            color: #8E8E93;
        }

        .reject-button:hover {
            background: #E5E5EA;
        }

        .action-button .material-icons {
            font-size: 16px;
            margin-right: 4px;
        }

        .report-section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-top: 20px;
        }

        .report-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 16px;
        }

        .report-card {
            display: flex;
            align-items: flex-start;
            margin-bottom: 16px;
            gap: 12px;
        }

        .report-card .material-icons {
            color: #FF6B6B;
            font-size: 24px;
            margin-top: 2px;
        }

        .report-content {
            flex: 1;
        }

        .report-text {
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
            line-height: 20px;
        }

        .report-insight {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
            line-height: 16px;
        }

        .view-report-button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px;
            border: 1px solid #FF6B6B;
            border-radius: 8px;
            background: white;
            color: #FF6B6B;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .view-report-button:hover {
            background: #FF6B6B;
            color: white;
        }

        .view-report-button .material-icons {
            font-size: 16px;
            margin-left: 4px;
        }

        .success-animation {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(76, 175, 80, 0.9);
            color: white;
            padding: 20px;
            border-radius: 16px;
            text-align: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .success-animation.show {
            opacity: 1;
        }

        .success-animation .material-icons {
            font-size: 48px;
            margin-bottom: 8px;
            animation: bounce 0.6s ease-in-out;
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 頂部漸變背景 -->
        <div class="header">
            <h1>智能關聯推薦</h1>
            <p>基於關鍵詞匹配和過往模式的智能推薦</p>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">3</div>
                    <div class="stat-label">總推薦數</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">1</div>
                    <div class="stat-label">已關聯</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">2</div>
                    <div class="stat-label">待處理</div>
                </div>
            </div>
        </div>

        <!-- 內容區域 -->
        <div class="content">
            <!-- 推薦1 - 晨跑 -->
            <div class="recommendation-card fade-in">
                <div class="card-header">
                    <div class="diary-info">
                        <div class="diary-title">今天的晨跑讓我感到充滿活力</div>
                        <div class="timestamp">2024-01-20 08:30</div>
                    </div>
                    <div class="status-badge status-pending">待處理</div>
                </div>
                <div class="diary-content">
                    早上6點起床去公園跑步，呼吸新鮮空氣，感受陽光的溫暖...
                </div>
                <div class="recommendation-title">智能推薦的願景：</div>
                <div class="vision-recommendation">
                    <div class="vision-card">
                        <div class="vision-header">
                            <div class="vision-info">
                                <div class="vision-title">健康生活</div>
                                <div class="match-reason">關鍵詞：運動、健康</div>
                            </div>
                            <div class="match-score">
                                <div class="match-percentage">95%</div>
                                <div class="match-label">匹配度</div>
                            </div>
                        </div>
                        <div class="action-buttons">
                            <button class="action-button accept-button" onclick="handleRecommendation(1, 1, 'accepted')">
                                <span class="material-icons">check</span>
                                建立關聯
                            </button>
                            <button class="action-button reject-button" onclick="handleRecommendation(1, 1, 'rejected')">
                                <span class="material-icons">close</span>
                                不相關
                            </button>
                        </div>
                    </div>
                </div>
                <div class="vision-recommendation">
                    <div class="vision-card">
                        <div class="vision-header">
                            <div class="vision-info">
                                <div class="vision-title">早起習慣</div>
                                <div class="match-reason">關鍵詞：早起、晨跑</div>
                            </div>
                            <div class="match-score">
                                <div class="match-percentage">85%</div>
                                <div class="match-label">匹配度</div>
                            </div>
                        </div>
                        <div class="action-buttons">
                            <button class="action-button accept-button" onclick="handleRecommendation(1, 2, 'accepted')">
                                <span class="material-icons">check</span>
                                建立關聯
                            </button>
                            <button class="action-button reject-button" onclick="handleRecommendation(1, 2, 'rejected')">
                                <span class="material-icons">close</span>
                                不相關
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 推薦2 - 項目提案 -->
            <div class="recommendation-card fade-in">
                <div class="card-header">
                    <div class="diary-info">
                        <div class="diary-title">完成了重要的項目提案</div>
                        <div class="timestamp">2024-01-19 18:45</div>
                    </div>
                    <div class="status-badge status-pending">待處理</div>
                </div>
                <div class="diary-content">
                    經過一週的努力，終於完成了客戶的項目提案，團隊合作很愉快...
                </div>
                <div class="recommendation-title">智能推薦的願景：</div>
                <div class="vision-recommendation">
                    <div class="vision-card">
                        <div class="vision-header">
                            <div class="vision-info">
                                <div class="vision-title">事業發展</div>
                                <div class="match-reason">關鍵詞：項目、工作成就</div>
                            </div>
                            <div class="match-score">
                                <div class="match-percentage">92%</div>
                                <div class="match-label">匹配度</div>
                            </div>
                        </div>
                        <div class="action-buttons">
                            <button class="action-button accept-button" onclick="handleRecommendation(2, 3, 'accepted')">
                                <span class="material-icons">check</span>
                                建立關聯
                            </button>
                            <button class="action-button reject-button" onclick="handleRecommendation(2, 3, 'rejected')">
                                <span class="material-icons">close</span>
                                不相關
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 推薦3 - 家庭時光 (已關聯) -->
            <div class="recommendation-card fade-in">
                <div class="card-header">
                    <div class="diary-info">
                        <div class="diary-title">和家人共度美好的週末</div>
                        <div class="timestamp">2024-01-18 20:15</div>
                    </div>
                    <div class="status-badge status-accepted">已關聯</div>
                </div>
                <div class="diary-content">
                    週末和父母一起做飯，聊天，感受到家庭的溫暖和愛...
                </div>
                <div class="recommendation-title">智能推薦的願景：</div>
                <div class="vision-recommendation">
                    <div class="vision-card">
                        <div class="vision-header">
                            <div class="vision-info">
                                <div class="vision-title">家庭關係</div>
                                <div class="match-reason">關鍵詞：家人、溫暖、愛</div>
                            </div>
                            <div class="match-score">
                                <div class="match-percentage">98%</div>
                                <div class="match-label">匹配度</div>
                            </div>
                        </div>
                        <div style="text-align: center; padding: 12px; background: #D4EDDA; border-radius: 8px; color: #155724;">
                            <span class="material-icons" style="vertical-align: middle; margin-right: 4px;">check_circle</span>
                            已成功建立關聯
                        </div>
                    </div>
                </div>
            </div>

            <!-- 關聯報告預覽 -->
            <div class="report-section">
                <div class="report-title">感恩與願景關聯報告</div>
                <div class="report-card">
                    <span class="material-icons">analytics</span>
                    <div class="report-content">
                        <div class="report-text">
                            本月已建立 1 個關聯，發現了以下潛在聯繫：
                        </div>
                        <div class="report-insight">
                            • 健康相關的感恩記錄主要集中在早晨時段
                        </div>
                        <div class="report-insight">
                            • 工作成就感與團隊合作呈現強關聯性
                        </div>
                        <div class="report-insight">
                            • 家庭時光的記錄頻率與整體幸福感正相關
                        </div>
                    </div>
                </div>
                <a href="#" class="view-report-button">
                    查看完整報告
                    <span class="material-icons">arrow_forward</span>
                </a>
            </div>
        </div>

        <!-- 成功動畫 -->
        <div class="success-animation" id="successAnimation">
            <div class="material-icons">check_circle</div>
            <div id="successText">關聯建立成功！</div>
        </div>
    </div>

    <script>
        // 處理推薦結果
        function handleRecommendation(diaryId, visionId, action) {
            const card = event.target.closest('.recommendation-card');
            const statusBadge = card.querySelector('.status-badge');
            const actionButtons = card.querySelector('.action-buttons');
            
            if (action === 'accepted') {
                statusBadge.textContent = '已關聯';
                statusBadge.className = 'status-badge status-accepted';
                
                // 替換按鈕為成功狀態
                actionButtons.innerHTML = `
                    <div style="text-align: center; padding: 12px; background: #D4EDDA; border-radius: 8px; color: #155724;">
                        <span class="material-icons" style="vertical-align: middle; margin-right: 4px;">check_circle</span>
                        已成功建立關聯
                    </div>
                `;
                
                // 顯示成功動畫
                showSuccessAnimation('關聯建立成功！');
                
                // 更新統計數據
                updateStats();
                
            } else if (action === 'rejected') {
                statusBadge.textContent = '已忽略';
                statusBadge.className = 'status-badge status-rejected';
                
                actionButtons.innerHTML = `
                    <div style="text-align: center; padding: 12px; background: #F8D7DA; border-radius: 8px; color: #721c24;">
                        <span class="material-icons" style="vertical-align: middle; margin-right: 4px;">cancel</span>
                        已忽略此推薦
                    </div>
                `;
                
                updateStats();
            }
            
            console.log(`${action === 'accepted' ? '建立關聯' : '忽略推薦'}：日記 ${diaryId} -> 願景 ${visionId}`);
        }

        // 顯示成功動畫
        function showSuccessAnimation(text) {
            const animation = document.getElementById('successAnimation');
            const textElement = document.getElementById('successText');
            
            textElement.textContent = text;
            animation.classList.add('show');
            
            setTimeout(() => {
                animation.classList.remove('show');
            }, 2000);
        }

        // 更新統計數據
        function updateStats() {
            const acceptedCards = document.querySelectorAll('.status-accepted').length;
            const pendingCards = document.querySelectorAll('.status-pending').length;
            
            const statCards = document.querySelectorAll('.stat-card .stat-number');
            statCards[1].textContent = acceptedCards; // 已關聯
            statCards[2].textContent = pendingCards;  // 待處理
        }

        // 頁面加載動畫
        document.addEventListener('DOMContentLoaded', function() {
            const animateElements = document.querySelectorAll('.fade-in');
            animateElements.forEach((element, index) => {
                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
