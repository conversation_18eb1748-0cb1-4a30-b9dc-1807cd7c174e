<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>願景進度追蹤 - 感恩與願景聯動模組</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            background: #F8F9FA;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            padding: 50px 20px 20px;
            border-radius: 0 0 25px 25px;
            text-align: center;
        }

        .header h1 {
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .header p {
            color: white;
            font-size: 16px;
            opacity: 0.9;
        }

        .content {
            padding: 20px;
        }

        .vision-card {
            background: white;
            border-radius: 16px;
            margin-bottom: 16px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .vision-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }

        .vision-gradient {
            background: linear-gradient(135deg, var(--vision-color), var(--vision-color-light));
            padding: 20px;
            display: flex;
            align-items: center;
            position: relative;
        }

        .progress-ring {
            margin-right: 16px;
            position: relative;
        }

        .progress-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: conic-gradient(white 0deg var(--progress-deg), rgba(255,255,255,0.3) var(--progress-deg) 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }

        .vision-info {
            flex: 1;
        }

        .vision-title {
            font-size: 18px;
            font-weight: bold;
            color: white;
            margin-bottom: 4px;
        }

        .vision-description {
            font-size: 14px;
            color: white;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .vision-stats {
            display: flex;
            gap: 16px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .stat-item .material-icons {
            font-size: 16px;
            color: white;
        }

        .stat-text {
            font-size: 12px;
            color: white;
        }

        .detail-button {
            padding: 8px;
            color: white;
        }

        .detail-button .material-icons {
            font-size: 16px;
        }

        /* 模態框樣式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: flex-end;
            z-index: 1000;
        }

        .modal-overlay.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 20px 20px 0 0;
            max-height: 80vh;
            width: 100%;
            max-width: 414px;
            overflow-y: auto;
            animation: slideUp 0.3s ease;
        }

        @keyframes slideUp {
            from { transform: translateY(100%); }
            to { transform: translateY(0); }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #E5E5EA;
        }

        .modal-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
        }

        .close-button {
            padding: 4px;
            cursor: pointer;
            color: #333;
        }

        .modal-scroll {
            padding: 0;
        }

        .detail-section {
            padding: 20px;
            border-bottom: 1px solid #F2F2F7;
        }

        .detail-section:last-child {
            border-bottom: none;
        }

        .detail-section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 16px;
        }

        .stats-grid {
            display: flex;
            gap: 8px;
        }

        .stat-card {
            flex: 1;
            background: #F8F9FA;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #8E8E93;
        }

        .emotion-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .emotion-name {
            font-size: 14px;
            color: #333;
            width: 60px;
        }

        .emotion-bar {
            flex: 1;
            height: 8px;
            background: #E5E5EA;
            border-radius: 4px;
            margin: 0 12px;
            overflow: hidden;
        }

        .emotion-progress {
            height: 100%;
            border-radius: 4px;
            transition: width 0.8s ease;
        }

        .emotion-count {
            font-size: 14px;
            color: #333;
            width: 30px;
            text-align: right;
        }

        .prediction-card {
            display: flex;
            align-items: center;
            background: #F8F9FA;
            border-radius: 12px;
            padding: 16px;
            gap: 12px;
        }

        .prediction-text {
            font-size: 14px;
            color: #333;
            flex: 1;
        }

        .prediction-date {
            font-weight: bold;
            color: #FF6B6B;
        }

        .milestone-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }

        .milestone-content {
            flex: 1;
            margin-left: 12px;
        }

        .milestone-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 4px;
        }

        .milestone-title.completed {
            text-decoration: line-through;
            color: #8E8E93;
        }

        .milestone-date {
            font-size: 12px;
            color: #8E8E93;
        }

        .milestone-progress-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .milestone-progress {
            width: 150px;
            height: 4px;
            background: #E5E5EA;
            border-radius: 2px;
            overflow: hidden;
        }

        .milestone-progress-fill {
            height: 100%;
            background: #FF6B6B;
            border-radius: 2px;
            transition: width 0.8s ease;
        }

        .milestone-progress-text {
            font-size: 12px;
            color: #333;
        }

        .celebration-icon {
            margin-left: 8px;
            color: #FFD700;
        }

        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 頂部漸變背景 -->
        <div class="header">
            <h1>願景進度追蹤</h1>
            <p>點擊願景卡片查看詳細統計和預測</p>
        </div>

        <!-- 內容區域 -->
        <div class="content">
            <!-- 健康生活願景 -->
            <div class="vision-card fade-in" data-vision="health" onclick="openModal('health')">
                <div class="vision-gradient" style="--vision-color: #FF6B6B; --vision-color-light: #FF6B6BCC;">
                    <div class="progress-ring">
                        <div class="progress-circle" style="--progress-deg: 270deg;">75%</div>
                    </div>
                    <div class="vision-info">
                        <div class="vision-title">健康生活</div>
                        <div class="vision-description">每天運動30分鐘，保持健康飲食</div>
                        <div class="vision-stats">
                            <div class="stat-item">
                                <span class="material-icons">favorite</span>
                                <span class="stat-text">45 記錄</span>
                            </div>
                            <div class="stat-item">
                                <span class="material-icons">schedule</span>
                                <span class="stat-text">每週5次</span>
                            </div>
                        </div>
                    </div>
                    <div class="detail-button">
                        <span class="material-icons">arrow_forward_ios</span>
                    </div>
                </div>
            </div>

            <!-- 事業發展願景 -->
            <div class="vision-card fade-in" data-vision="career" onclick="openModal('career')">
                <div class="vision-gradient" style="--vision-color: #4ECDC4; --vision-color-light: #4ECDC4CC;">
                    <div class="progress-ring">
                        <div class="progress-circle" style="--progress-deg: 162deg;">45%</div>
                    </div>
                    <div class="vision-info">
                        <div class="vision-title">事業發展</div>
                        <div class="vision-description">提升專業技能，獲得晉升機會</div>
                        <div class="vision-stats">
                            <div class="stat-item">
                                <span class="material-icons">favorite</span>
                                <span class="stat-text">28 記錄</span>
                            </div>
                            <div class="stat-item">
                                <span class="material-icons">schedule</span>
                                <span class="stat-text">每週3次</span>
                            </div>
                        </div>
                    </div>
                    <div class="detail-button">
                        <span class="material-icons">arrow_forward_ios</span>
                    </div>
                </div>
            </div>

            <!-- 人際關係願景 -->
            <div class="vision-card fade-in" data-vision="relationship" onclick="openModal('relationship')">
                <div class="vision-gradient" style="--vision-color: #45B7D1; --vision-color-light: #45B7D1CC;">
                    <div class="progress-ring">
                        <div class="progress-circle" style="--progress-deg: 223deg;">62%</div>
                    </div>
                    <div class="vision-info">
                        <div class="vision-title">人際關係</div>
                        <div class="vision-description">建立更深層的友誼和家庭關係</div>
                        <div class="vision-stats">
                            <div class="stat-item">
                                <span class="material-icons">favorite</span>
                                <span class="stat-text">35 記錄</span>
                            </div>
                            <div class="stat-item">
                                <span class="material-icons">schedule</span>
                                <span class="stat-text">每週4次</span>
                            </div>
                        </div>
                    </div>
                    <div class="detail-button">
                        <span class="material-icons">arrow_forward_ios</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 詳細統計模態框 -->
        <div class="modal-overlay" id="modalOverlay">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title" id="modalTitle">健康生活</div>
                    <div class="close-button" onclick="closeModal()">
                        <span class="material-icons">close</span>
                    </div>
                </div>

                <div class="modal-scroll">
                    <!-- 詳細統計 -->
                    <div class="detail-section">
                        <div class="detail-section-title">詳細統計</div>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number" id="totalRecords">45</div>
                                <div class="stat-label">相關記錄總數</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="frequency">每週5次</div>
                                <div class="stat-label">記錄頻率</div>
                            </div>
                        </div>
                    </div>

                    <!-- 情感分布 -->
                    <div class="detail-section">
                        <div class="detail-section-title">情感分布</div>
                        <div id="emotionDistribution">
                            <!-- 動態生成情感分布 -->
                        </div>
                    </div>

                    <!-- 願景實現預測 -->
                    <div class="detail-section">
                        <div class="detail-section-title">實現預測</div>
                        <div class="prediction-card">
                            <span class="material-icons" style="color: var(--vision-color);">trending_up</span>
                            <div class="prediction-text">
                                基於當前進度和記錄頻率，預計在 
                                <span class="prediction-date" id="predictedDate">2024-03-15</span> 
                                達成目標
                            </div>
                        </div>
                    </div>

                    <!-- 里程碑 -->
                    <div class="detail-section">
                        <div class="detail-section-title">里程碑進度</div>
                        <div id="milestones">
                            <!-- 動態生成里程碑 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 願景數據
        const visionData = {
            health: {
                title: '健康生活',
                totalRecords: 45,
                frequency: '每週5次',
                predictedDate: '2024-03-15',
                color: '#FF6B6B',
                emotionDistribution: {
                    '喜悅': 15,
                    '平靜': 12,
                    '滿足': 10,
                    '希望': 8
                },
                milestones: [
                    {id: 1, title: '開始運動習慣', completed: true, date: '2024-01-01'},
                    {id: 2, title: '連續運動30天', completed: true, date: '2024-01-30'},
                    {id: 3, title: '達成體重目標', completed: false, progress: 0.6},
                    {id: 4, title: '完成馬拉松', completed: false, progress: 0.2}
                ]
            },
            career: {
                title: '事業發展',
                totalRecords: 28,
                frequency: '每週3次',
                predictedDate: '2024-06-20',
                color: '#4ECDC4',
                emotionDistribution: {
                    '希望': 12,
                    '滿足': 8,
                    '喜悅': 5,
                    '平靜': 3
                },
                milestones: [
                    {id: 1, title: '完成專業課程', completed: true, date: '2024-01-15'},
                    {id: 2, title: '獲得認證', completed: false, progress: 0.8},
                    {id: 3, title: '申請晉升', completed: false, progress: 0.1}
                ]
            },
            relationship: {
                title: '人際關係',
                totalRecords: 35,
                frequency: '每週4次',
                predictedDate: '2024-04-10',
                color: '#45B7D1',
                emotionDistribution: {
                    '愛': 18,
                    '感動': 10,
                    '喜悅': 5,
                    '平靜': 2
                },
                milestones: [
                    {id: 1, title: '每週與家人聚餐', completed: true, date: '2024-01-01'},
                    {id: 2, title: '結交新朋友', completed: true, date: '2024-01-20'},
                    {id: 3, title: '深化友誼', completed: false, progress: 0.7}
                ]
            }
        };

        // 打開模態框
        function openModal(visionKey) {
            const vision = visionData[visionKey];
            const modal = document.getElementById('modalOverlay');
            
            // 設置標題和基本信息
            document.getElementById('modalTitle').textContent = vision.title;
            document.getElementById('totalRecords').textContent = vision.totalRecords;
            document.getElementById('frequency').textContent = vision.frequency;
            document.getElementById('predictedDate').textContent = vision.predictedDate;
            
            // 設置顏色變量
            document.documentElement.style.setProperty('--vision-color', vision.color);
            
            // 生成情感分布
            generateEmotionDistribution(vision.emotionDistribution, vision.color);
            
            // 生成里程碑
            generateMilestones(vision.milestones);
            
            // 顯示模態框
            modal.classList.add('show');
        }

        // 關閉模態框
        function closeModal() {
            document.getElementById('modalOverlay').classList.remove('show');
        }

        // 生成情感分布
        function generateEmotionDistribution(emotions, color) {
            const container = document.getElementById('emotionDistribution');
            const maxValue = Math.max(...Object.values(emotions));
            
            container.innerHTML = '';
            
            Object.entries(emotions).forEach(([emotion, count]) => {
                const percentage = (count / maxValue) * 100;
                
                const emotionItem = document.createElement('div');
                emotionItem.className = 'emotion-item';
                emotionItem.innerHTML = `
                    <div class="emotion-name">${emotion}</div>
                    <div class="emotion-bar">
                        <div class="emotion-progress" style="width: ${percentage}%; background-color: ${color};"></div>
                    </div>
                    <div class="emotion-count">${count}</div>
                `;
                
                container.appendChild(emotionItem);
            });
        }

        // 生成里程碑
        function generateMilestones(milestones) {
            const container = document.getElementById('milestones');
            container.innerHTML = '';
            
            milestones.forEach(milestone => {
                const milestoneItem = document.createElement('div');
                milestoneItem.className = 'milestone-item';
                
                const iconName = milestone.completed ? 'check_circle' : 'radio_button_unchecked';
                const iconColor = milestone.completed ? '#4CAF50' : '#E5E5EA';
                
                let progressHtml = '';
                if (milestone.completed) {
                    progressHtml = `<div class="milestone-date">完成於 ${milestone.date}</div>`;
                } else if (milestone.progress) {
                    const progressPercentage = Math.round(milestone.progress * 100);
                    progressHtml = `
                        <div class="milestone-progress-container">
                            <div class="milestone-progress">
                                <div class="milestone-progress-fill" style="width: ${progressPercentage}%;"></div>
                            </div>
                            <div class="milestone-progress-text">${progressPercentage}%</div>
                        </div>
                    `;
                }
                
                const celebrationIcon = (milestone.progress >= 0.25 && milestone.progress < 1) ? 
                    '<span class="material-icons celebration-icon">celebration</span>' : '';
                
                milestoneItem.innerHTML = `
                    <span class="material-icons" style="color: ${iconColor};">${iconName}</span>
                    <div class="milestone-content">
                        <div class="milestone-title ${milestone.completed ? 'completed' : ''}">${milestone.title}</div>
                        ${progressHtml}
                    </div>
                    ${celebrationIcon}
                `;
                
                container.appendChild(milestoneItem);
            });
        }

        // 點擊模態框外部關閉
        document.getElementById('modalOverlay').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 頁面加載動畫
        document.addEventListener('DOMContentLoaded', function() {
            const animateElements = document.querySelectorAll('.fade-in');
            animateElements.forEach((element, index) => {
                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
